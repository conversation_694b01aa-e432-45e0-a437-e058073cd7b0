#!/usr/bin/env python3
"""
Test PATCH Calls

This script tests the PATCH call functionality between the main server (8081)
and the dummy Java server (8082).
"""

import requests
import json
import time
from datetime import datetime

def test_patch_functionality():
    """Test the PATCH call functionality"""
    
    print("=" * 60)
    print("TESTING PATCH CALL FUNCTIONALITY")
    print("=" * 60)
    
    # Test data for entity extraction request
    test_request = {
        "website_url": "https://example.com",
        "org_id": 12345,  # Test with integer org_id
        "scrape_request_ref_id": f"test_{int(time.time())}",
        "use_merged_process": True,
        "use_openai_fallback": True,
        "force_reprocess": True
    }
    
    print("1. Testing with integer org_id...")
    print(f"Request data: {json.dumps(test_request, indent=2)}")
    
    # Send request to main server
    try:
        print("\nSending request to main server (port 8081)...")
        response = requests.post(
            "http://localhost:8081/api/extract-entities",
            json=test_request,
            timeout=30
        )
        
        print(f"Response status: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        
        if response.status_code == 200:
            print("✅ Main server accepted request and returned 200 OK")
            print("⏳ Background processing should be happening...")
            
            # Wait a bit for background processing
            print("\nWaiting 10 seconds for background processing...")
            time.sleep(10)
            
            # Check dummy Java server for received requests
            print("\n2. Checking dummy Java server for received PATCH calls...")
            java_response = requests.get("http://localhost:8082/api/requests/latest")
            
            if java_response.status_code == 200:
                java_data = java_response.json()
                if "latest_request" in java_data:
                    print("✅ Dummy Java server received PATCH call!")
                    print("Latest request received:")
                    print(json.dumps(java_data["latest_request"], indent=2))
                    
                    # Check org_id type preservation
                    received_body = java_data["latest_request"].get("body", {})
                    received_org_id = received_body.get("org_id")
                    print(f"\n🔍 Org ID type preservation check:")
                    print(f"   Sent: {test_request['org_id']} (type: {type(test_request['org_id']).__name__})")
                    print(f"   Received: {received_org_id} (type: {type(received_org_id).__name__})")
                    
                    if type(received_org_id) == type(test_request['org_id']):
                        print("✅ Org ID type preserved correctly!")
                    else:
                        print("❌ Org ID type not preserved!")
                        
                else:
                    print("❌ No requests received by dummy Java server")
            else:
                print(f"❌ Error checking dummy Java server: {java_response.status_code}")
                
        else:
            print(f"❌ Main server returned error: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to main server. Is it running on port 8081?")
    except Exception as e:
        print(f"❌ Error testing main server: {e}")
    
    print("\n" + "=" * 60)
    
    # Test with string org_id
    print("3. Testing with string org_id...")
    test_request_str = test_request.copy()
    test_request_str["org_id"] = "string_org_123"
    test_request_str["scrape_request_ref_id"] = f"test_str_{int(time.time())}"
    
    print(f"Request data: {json.dumps(test_request_str, indent=2)}")
    
    try:
        print("\nSending request to main server (port 8081)...")
        response = requests.post(
            "http://localhost:8081/api/extract-entities",
            json=test_request_str,
            timeout=30
        )
        
        print(f"Response status: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        
        if response.status_code == 200:
            print("✅ Main server accepted string org_id request")
            
            # Wait for background processing
            print("\nWaiting 10 seconds for background processing...")
            time.sleep(10)
            
            # Check latest request
            java_response = requests.get("http://localhost:8082/api/requests/latest")
            if java_response.status_code == 200:
                java_data = java_response.json()
                if "latest_request" in java_data:
                    received_body = java_data["latest_request"].get("body", {})
                    received_org_id = received_body.get("org_id")
                    print(f"\n🔍 String Org ID type preservation check:")
                    print(f"   Sent: {test_request_str['org_id']} (type: {type(test_request_str['org_id']).__name__})")
                    print(f"   Received: {received_org_id} (type: {type(received_org_id).__name__})")
                    
                    if type(received_org_id) == type(test_request_str['org_id']):
                        print("✅ String Org ID type preserved correctly!")
                    else:
                        print("❌ String Org ID type not preserved!")
                        
    except Exception as e:
        print(f"❌ Error testing string org_id: {e}")
    
    print("\n" + "=" * 60)
    print("TESTING COMPLETE")
    print("=" * 60)
    print("\nTo view all received requests on dummy Java server:")
    print("GET http://localhost:8082/api/requests")
    print("\nTo clear stored requests:")
    print("DELETE http://localhost:8082/api/requests")
    print("\nTo check server health:")
    print("GET http://localhost:8081/health (main server)")
    print("GET http://localhost:8082/health (dummy Java server)")


def check_servers():
    """Check if both servers are running"""
    print("Checking server status...")
    
    # Check main server
    try:
        response = requests.get("http://localhost:8081/health", timeout=5)
        if response.status_code == 200:
            print("✅ Main server (8081) is running")
        else:
            print(f"❌ Main server (8081) returned {response.status_code}")
    except:
        print("❌ Main server (8081) is not accessible")
    
    # Check dummy Java server
    try:
        response = requests.get("http://localhost:8082/health", timeout=5)
        if response.status_code == 200:
            print("✅ Dummy Java server (8082) is running")
        else:
            print(f"❌ Dummy Java server (8082) returned {response.status_code}")
    except:
        print("❌ Dummy Java server (8082) is not accessible")


if __name__ == "__main__":
    print("Entity Extraction PATCH Call Tester")
    print("=" * 40)
    
    # Check servers first
    check_servers()
    
    print("\nPress Enter to start testing, or Ctrl+C to exit...")
    try:
        input()
        test_patch_functionality()
    except KeyboardInterrupt:
        print("\nTesting cancelled by user")
