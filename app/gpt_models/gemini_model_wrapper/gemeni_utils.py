"""
Centralized Gemini utils module with comprehensive error handling
"""
import json
from typing import Dict, Any, Optional, Union
import os
import time
import threading
import concurrent.futures
import dotenv
from datetime import datetime
import google.genai as genai
from google.genai import types
from app.utils.logger import ConsoleLogger
from app.Extractor.utils.api_response_logger import api_logger

dotenv.load_dotenv(".env")

# Default configuration constants
DEFAULT_MODEL = "gemini-2.5-flash"
DEFAULT_TIMEOUT = 120
DEFAULT_MAX_RETRIES = 3
DEFAULT_RATE_LIMIT_DELAY = 5
DEFAULT_MAX_OUTPUT_TOKENS = 4000  # REDUCED from 10000 to prevent MAX_TOKENS errors
DEFAULT_TEMPERATURE = 0
DEFAULT_TOP_P = 0.95
DEFAULT_SEED = 0

class GeminiResponseConfig:
    """Configuration class for Gemini API calls"""
    
    def __init__(
        self,
        model_name: str = DEFAULT_MODEL,
        timeout_seconds: int = DEFAULT_TIMEOUT,
        max_retries: int = DEFAULT_MAX_RETRIES,
        rate_limit_delay: int = DEFAULT_RATE_LIMIT_DELAY,
        max_output_tokens: int = DEFAULT_MAX_OUTPUT_TOKENS,
        temperature: float = DEFAULT_TEMPERATURE,
        top_p: float = DEFAULT_TOP_P,
        seed: int = DEFAULT_SEED,
        enable_url_context: bool = True,
        enable_google_search: bool = False,
        maximum_remote_calls: int = 20000  # Reduced to 20k to match hardcoded value and prevent API errors
    ):
        self.model_name = model_name
        self.timeout_seconds = timeout_seconds
        self.max_retries = max_retries
        self.rate_limit_delay = rate_limit_delay
        self.max_output_tokens = max_output_tokens
        self.temperature = temperature
        self.top_p = top_p
        self.seed = seed
        self.enable_url_context = enable_url_context
        self.enable_google_search = enable_google_search
        self.maximum_remote_calls = maximum_remote_calls

def clean_json_response(response: str) -> Dict[str, Any]:
    """
    Clean and parse JSON response with comprehensive error handling
    
    Args:
        response (str): Raw response string
        
    Returns:
        Dict[str, Any]: Parsed JSON or default error response
    """
    logger = ConsoleLogger("json_parser")
    
    try:
        if not response or not isinstance(response, str):
            logger.error("Invalid response type or empty response", {"response_type": type(response)})
            return {"error": "Invalid or empty response", "success": False}
        
        response = response.strip()
        
        # Handle markdown code blocks
        if "```json" in response:
            json_str = response.split("```json")[1].split("```")[0].strip()
        elif "```" in response:
            json_str = response.split("```")[1].split("```")[0].strip()
        else:
            json_str = response
        
        parsed_json = json.loads(json_str)
        logger.info("Successfully parsed JSON response")
        return parsed_json
        
    except json.JSONDecodeError as e:
        logger.error("JSON decode error", {"error": str(e), "response_sample": response[:200]})
        return {"error": f"JSON parsing failed: {str(e)}", "success": False, "raw_response": response[:500]}
    except Exception as e:
        logger.error("Unexpected error in JSON parsing", {"error": str(e)})
        return {"error": f"Unexpected parsing error: {str(e)}", "success": False}

def get_gemini_response(
    prompt: str, 
    config: Optional[GeminiResponseConfig] = None,
    context_info: Optional[Dict[str, Any]] = None
) -> str:
    
    # Use default config if none provided
    if config is None:
        config = GeminiResponseConfig()

    # Add small random delay to prevent rate limiting
    import random
    time.sleep(random.randint(1, 3))
    # Set up logger with context
    context_str = f"{context_info.get('task_type', 'general')}_{context_info.get('website', 'unknown')}" if context_info else "gemini_api"
    logger = ConsoleLogger(context_str)
    
    # Validate inputs
    if not prompt or not isinstance(prompt, str):
        logger.error("Invalid prompt provided", {"prompt_type": type(prompt), "prompt_length": len(prompt) if prompt else 0})
        return "Error: Invalid or empty prompt provided"
    
    if not os.getenv("GEMINI"):
        logger.error("GEMINI API key not found in environment variables")
        return "Error: GEMINI API key not configured"
    
    logger.info(
        "Starting Gemini API call",
        {
            "model": config.model_name,
            "timeout": config.timeout_seconds,
            "max_retries": config.max_retries,
            "prompt_length": len(prompt),
            "context": context_info or {}
        }
    )
    
    def make_api_call():
        """Thread-safe API call function"""
        # Initialize Gemini client
        client = genai.Client(api_key=os.getenv("GEMINI"))

        # Configure tools based on settings
        tools = []
        if config.enable_google_search:
            tools.append(types.Tool(google_search=types.GoogleSearch()))
        if config.enable_url_context:
            tools.append(types.Tool(url_context=types.UrlContext()))

        # System prompt for JSON formatting (currently not used in API call)
        # system_prompt = "always answer in json format starting with ```json"

        # Make API call with proper tool configuration based on config settings
        response = client.models.generate_content(
            model=config.model_name,
            contents=prompt,
            config=types.GenerateContentConfig(
                tools=tools,  # Use the tools configured above based on config settings
                automatic_function_calling=types.AutomaticFunctionCallingConfig(maximum_remote_calls=config.maximum_remote_calls),
                temperature = config.temperature,
                top_p = config.top_p,
                seed = config.seed,
                max_output_tokens = config.max_output_tokens,
            ),
        )
        return response

    retry_count = 0
    last_error = None

    while retry_count < config.max_retries:
        try:
            logger.info(f"Gemini API attempt {retry_count + 1}/{config.max_retries}")

            # Use ThreadPoolExecutor with timeout for thread-safe timeout handling
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(make_api_call)
                try:
                    response = future.result(timeout=config.timeout_seconds)
                except concurrent.futures.TimeoutError:
                    raise TimeoutError(f"Gemini API call timed out after {config.timeout_seconds} seconds")
            
            # Log usage metadata
            if hasattr(response, 'usage_metadata') and response.usage_metadata:
                logger.info(f"Gemini API Usage: {response.usage_metadata}")
            
            # Validate response structure
            if not hasattr(response, 'candidates') or not response.candidates:
                raise ValueError("No candidates in Gemini response")
            
            candidate = response.candidates[0]
            
            # Check completion status
            if not hasattr(candidate, 'finish_reason') or candidate.finish_reason != 'STOP':
                finish_reason = getattr(candidate, 'finish_reason', 'unknown')
                if finish_reason == 'MAX_TOKENS':
                    # Special handling for MAX_TOKENS error - try to extract partial response
                    if response.text and len(response.text) > 100000:
                        logger.warning(f"MAX_TOKENS reached but partial response available. Using partial response.")
                        return response.text
                    else:
                        raise ValueError(f"Response generation incomplete. Finish reason: {finish_reason}. Consider reducing max_output_tokens further.")
                else:
                    raise ValueError(f"Response generation incomplete. Finish reason: {finish_reason}")
            
            # Extract text content
            if not response.text:
                raise ValueError("Empty response text from Gemini")
            
            logger.info(
                "Gemini API call successful",
                {
                    "attempt": retry_count + 1,
                    "response_length": len(response.text),
                    "finish_reason": candidate.finish_reason
                }
            )
            
            # Rate limiting delay
            time.sleep(config.rate_limit_delay)
            
            # Log Gemini response to JSON file and server.log
            api_logger.log_gemini_response(
                prompt=prompt,
                response=response.text,
                model_name=config.model_name if config else DEFAULT_MODEL,
                request_id=context_info.get('request_id') if context_info else None,
                context={
                    "config": {
                        "model": config.model_name if config else DEFAULT_MODEL,
                        "temperature": config.temperature if config else DEFAULT_TEMPERATURE,
                        "max_output_tokens": config.max_output_tokens if config else DEFAULT_MAX_OUTPUT_TOKENS,
                        "enable_url_context": config.enable_url_context if config else True,
                        "enable_google_search": config.enable_google_search if config else False
                    } if config else None,
                    "context_info": context_info,
                    "usage_metadata": getattr(response, 'usage_metadata', None),
                    "timestamp": datetime.now().isoformat()
                }
            )
            
            return response.text

        except TimeoutError as e:
            last_error = e
            retry_count += 1
            logger.warning(
                f"Gemini API timeout on attempt {retry_count}",
                {"error": str(e), "timeout": config.timeout_seconds}
            )

        except Exception as e:
            last_error = e
            retry_count += 1
            logger.error(
                f"Gemini API error on attempt {retry_count}",
                {"error": str(e), "error_type": type(e).__name__}
            )

        # Wait before retrying (exponential backoff)
        if retry_count < config.max_retries:
            wait_time = config.rate_limit_delay * retry_count
            logger.info(f"Waiting {wait_time}s before retry...")
            time.sleep(wait_time)

    # All retries exhausted
    error_msg = f"No response from Gemini API after {config.max_retries} attempts. Last error: {str(last_error)}"
    logger.error(
        "All Gemini API attempts failed",
        {
            "max_retries": config.max_retries,
            "last_error": str(last_error),
            "context": context_info or {}
        }
    )
    
    return error_msg

# Backwards compatibility functions
def get_gemini_response_legacy(prompt, model_name="gemini-2.5-flash"):
    """Legacy function for backwards compatibility"""
    config = GeminiResponseConfig(
        model_name=model_name,
        enable_google_search=False,  # Enable Google Search for general tasksalse,  # Disable Google Search to focus on URL visiting
        enable_url_context=True,    # Enable URL context tool for URL analysis tasksalse,    # Enable URL context tool for URL analysis tasks
        max_output_tokens=20000,      # INCREASED for entity extraction to handle comprehensive responses
        rate_limit_delay=2,
        temperature=0
    )
    context_info = {"task_type": "legacy"}
    return get_gemini_response(prompt, config, context_info)

# Utility function for prompt truncation (kept for potential future use)
def truncate_prompt_for_hard_classification(prompt: str, max_chars: int = 6000) -> str:
    """
    Truncate prompt to prevent MAX_TOKENS error while preserving structure

    Args:
        prompt (str): Original prompt
        max_chars (int): Maximum characters to keep

    Returns:
        str: Truncated prompt
    """
    if len(prompt) <= max_chars:
        return prompt

    # Find the last complete URL entry before the limit
    lines = prompt.split('\n')
    truncated_lines = []
    current_length = 0

    for line in lines:
        if current_length + len(line) + 1 <= max_chars:
            truncated_lines.append(line)
            current_length += len(line) + 1
        else:
            # Add a note about truncation
            truncated_lines.append("... (content truncated to prevent token overflow)")
            break

    return '\n'.join(truncated_lines)
