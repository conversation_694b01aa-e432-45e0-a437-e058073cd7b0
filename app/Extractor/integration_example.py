"""
Integration Example for Entity Extractor Tool

This file shows how to integrate the Entity Extractor tool with the main FastAPI application.
"""

# Example of how to add the Entity Extractor router to your main FastAPI app

"""
In your main.py or app.py file, add the following:

from fastapi import FastAPI
from Extractor.routers.entity_extraction_router import router as entity_router

app = FastAPI()

# Include the Entity Extractor router
app.include_router(entity_router)

# Also run the database setup to create tables
from Extractor.database_setup import create_entity_extractor_tables

@app.on_event("startup")
async def startup_event():
    # Create Entity Extractor tables on startup
    create_entity_extractor_tables()
"""

# Example usage of the Entity Extractor service directly

def example_direct_usage():
    """
    Example of using the Entity Extractor service directly without API
    """
    from Extractor.models.request_models import EntityExtractionRequest
    from Extractor.services.entity_extractor_orchestrator import EntityExtractorOrchestrator
    
    # Create a request (force_reprocess and use_openai_fallback are now always True)
    request = EntityExtractionRequest(
        scrape_request_ref_id="req_example_123",
        website_url="https://example.com",
        org_id="default",
        force_reprocess=True,  # Always forced to True for merged processing
        use_openai_fallback=True  # Always forced to True for merged processing
    )
    
    # Process the request
    orchestrator = EntityExtractorOrchestrator()
    response = orchestrator.process_entity_extraction(request)
    
    print(f"Analysis ID: {response.analysis_id}")
    print(f"Status: {response.processing_status}")
    print(f"Legal Name: {response.legal_name}")
    print(f"Business Email: {response.business_email}")
    print(f"Has Jurisdiction Law: {response.has_jurisdiction_law}")
    print(f"Accepts International Orders: {response.accepts_international_orders}")
    
    return response


# Example of testing individual services

def example_test_entity_extraction():
    """
    Example of testing the entity extraction service directly
    """
    from Extractor.services.entity_extraction_service import EntityExtractionService
    
    service = EntityExtractionService(
        scrape_request_ref_id="req_example_123",
        website_url="https://example.com",
        org_id="default"
    )
    
    results = service.extract_entities(use_openai_fallback=True)
    print("Entity Extraction Results:", results)
    
    return results


def example_test_terms_analysis():
    """
    Example of testing the terms analysis service directly
    """
    from Extractor.services.terms_analysis_service import TermsAnalysisService
    
    service = TermsAnalysisService(
        scrape_request_ref_id="req_example_123",
        website_url="https://example.com",
        org_id="default"
    )
    
    results = service.analyze_terms_conditions(use_openai_fallback=True)
    print("Terms Analysis Results:", results)
    
    return results


def example_test_shipping_analysis():
    """
    Example of testing the shipping analysis service directly
    """
    from Extractor.services.shipping_analysis_service import ShippingAnalysisService
    
    service = ShippingAnalysisService(
        scrape_request_ref_id="req_example_123",
        website_url="https://example.com",
        org_id="default"
    )
    
    results = service.analyze_shipping_policy(use_openai_fallback=True)
    print("Shipping Analysis Results:", results)
    
    return results


def example_test_url_retrieval():
    """
    Example of testing the URL data retrieval service
    """
    from Extractor.services.url_data_retrieval import UrlDataRetrievalService
    
    service = UrlDataRetrievalService(
        scrape_request_ref_id="req_example_123",
        org_id="default"
    )
    
    # Test getting URLs from database
    urls_data = service.get_urls_from_database()
    print(f"Found {len(urls_data)} URLs in database")
    
    # Test getting soft classifier results
    soft_results = service.get_soft_classifier_results()
    print("Soft Classification Results:", soft_results)
    
    # Test getting hard classifier results
    hard_results = service.get_hard_classifier_results()
    print("Hard Classification Results:", hard_results)
    
    # Test getting policy URLs
    policy_urls = service.get_policy_urls()
    print("Policy URLs:", policy_urls)
    
    return {
        "urls_data": urls_data,
        "soft_results": soft_results,
        "hard_results": hard_results,
        "policy_urls": policy_urls
    }


# Example API client usage

def example_api_client():
    """
    Example of using the Entity Extractor API endpoints
    """
    import requests
    import json
    
    base_url = "http://localhost:8000"  # Adjust to your API base URL
    
    # Start entity extraction (API now returns immediate 200 OK response)
    request_data = {
        "scrape_request_ref_id": "req_example_123",
        "website_url": "https://example.com",
        "org_id": "default",
        "force_reprocess": True,  # Always forced to True for merged processing
        "use_openai_fallback": True  # Always forced to True for merged processing
    }

    print("Starting entity extraction (immediate response)...")
    response = requests.post(f"{base_url}/entity-extraction/analyze", json=request_data)
    
    if response.status_code == 200:
        result = response.json()
        print(f"Request accepted! Processing in background...")
        print(f"Status: {result.get('status')}")
        print(f"Message: {result.get('message')}")
        print(f"Scrape Request Ref ID: {result.get('scrape_request_ref_id')}")
        print(f"Website URL: {result.get('website_url')}")
        print(f"Org ID: {result.get('org_id')}")
        print(f"Processing Settings: {result.get('processing_settings')}")
        print(f"Timestamp: {result.get('timestamp')}")
        print("Note: Processing happens in background. Results will be sent to Java server via PATCH.")

        return result
    else:
        print(f"Error: {response.status_code} - {response.text}")
        return None


if __name__ == "__main__":
    print("Entity Extractor Integration Examples")
    print("=====================================")
    
    # Uncomment the examples you want to test:
    
    # example_direct_usage()
    # example_test_url_retrieval()
    # example_test_entity_extraction()
    # example_test_terms_analysis()
    # example_test_shipping_analysis()
    # example_api_client()
    
    print("\nTo use these examples, uncomment the function calls above and ensure:")
    print("1. Database is set up with Entity Extractor tables")
    print("2. You have existing scrape_request_ref_id with classified URLs")
    print("3. Environment variables are configured (GEMINI, OPENAI_API_KEY)")
    print("4. FastAPI app is running (for API examples)")
