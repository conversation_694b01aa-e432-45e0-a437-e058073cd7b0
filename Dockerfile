# Use the official lightweight Python image as a base
FROM python:3.12-slim

# Set the working directory inside the container
WORKDIR /app

# Copy the requirements file and install dependencies
# This is done first to leverage <PERSON><PERSON>'s layer caching
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy the rest of the application code into the container
# The .dockerignore file will prevent unwanted files from being copied.
COPY . .

# Cloud Run sets the PORT environment variable.
# We'll expose this port. The default is 8080.
ENV PORT 8080
EXPOSE 8080

# Use <PERSON><PERSON> to run the application. The `main:app` part
# refers to the Flask app instance named `app` in `main.py`.
# The `--bind` option tells <PERSON><PERSON> to listen on all interfaces.
CMD ["gunicorn", "-k", "uvicorn.workers.UvicornWorker", "--workers", "4", "--bind", "0.0.0.0:8080", "main:app"]
