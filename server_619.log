nohup: ignoring input
INFO:     Started server process [94278]
INFO:     Waiting for application startup.
2025-08-03 19:10:12,727 - app.main - INFO - Initializing Entity Extraction API
2025-08-03 19:10:13,092 INFO sqlalchemy.engine.Engine SELECT DATABASE()
2025-08-03 19:10:13,092 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-03 19:10:13,093 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 19:10:13,093 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 19:10:13,191 INFO sqlalchemy.engine.Engine SELECT @@sql_mode
2025-08-03 19:10:13,191 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-03 19:10:13,192 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 19:10:13,192 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 19:10:13,242 INFO sqlalchemy.engine.Engine SELECT @@lower_case_table_names
2025-08-03 19:10:13,242 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-03 19:10:13,242 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 19:10:13,242 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 19:10:13,344 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:10:13,344 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:10:13,344 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`website_urls_gemini`
2025-08-03 19:10:13,344 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`website_urls_gemini`
2025-08-03 19:10:13,344 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 19:10:13,344 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 19:10:13,393 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`general_logs_gemini`
2025-08-03 19:10:13,393 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`general_logs_gemini`
2025-08-03 19:10:13,393 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 19:10:13,393 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 19:10:13,444 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`mcc_url_classification_gemini`
2025-08-03 19:10:13,444 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`mcc_url_classification_gemini`
2025-08-03 19:10:13,444 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 19:10:13,444 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 19:10:13,493 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`policy_url_classification_gemini`
2025-08-03 19:10:13,493 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`policy_url_classification_gemini`
2025-08-03 19:10:13,493 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 19:10:13,493 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 19:10:13,546 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`policy_analysis_new_gemini`
2025-08-03 19:10:13,546 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`policy_analysis_new_gemini`
2025-08-03 19:10:13,546 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 19:10:13,546 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 19:10:13,616 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`entity_extraction_analysis`
2025-08-03 19:10:13,616 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`entity_extraction_analysis`
2025-08-03 19:10:13,616 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 19:10:13,616 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 19:10:13,682 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`entity_extraction_url_analysis`
2025-08-03 19:10:13,682 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`entity_extraction_url_analysis`
2025-08-03 19:10:13,682 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 19:10:13,682 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 19:10:13,743 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:10:13,743 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 19:10:13,841 - app.main - INFO - Database initialized successfully
Creating Entity Extractor database tables...
2025-08-03 19:10:13,898 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:10:13,898 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:10:13,898 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`website_urls_gemini`
2025-08-03 19:10:13,898 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`website_urls_gemini`
2025-08-03 19:10:13,899 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 19:10:13,899 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 19:10:13,954 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`general_logs_gemini`
2025-08-03 19:10:13,954 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`general_logs_gemini`
2025-08-03 19:10:13,954 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 19:10:13,954 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 19:10:14,002 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`mcc_url_classification_gemini`
2025-08-03 19:10:14,002 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`mcc_url_classification_gemini`
2025-08-03 19:10:14,002 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 19:10:14,002 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 19:10:14,053 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`policy_url_classification_gemini`
2025-08-03 19:10:14,053 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`policy_url_classification_gemini`
2025-08-03 19:10:14,053 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 19:10:14,053 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 19:10:14,109 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`policy_analysis_new_gemini`
2025-08-03 19:10:14,109 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`policy_analysis_new_gemini`
2025-08-03 19:10:14,109 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 19:10:14,109 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 19:10:14,176 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`entity_extraction_analysis`
2025-08-03 19:10:14,176 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`entity_extraction_analysis`
2025-08-03 19:10:14,176 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 19:10:14,176 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 19:10:14,235 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`entity_extraction_url_analysis`
2025-08-03 19:10:14,235 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`entity_extraction_url_analysis`
2025-08-03 19:10:14,235 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 19:10:14,235 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 19:10:14,292 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:10:14,292 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 19:10:14,401 - app.main - INFO - Entity Extractor tables initialized successfully
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
✅ Entity Extractor database tables created successfully!
Tables created:
  - entity_extraction_analysis
  - entity_extraction_url_analysis
INFO:     127.0.0.1:58338 - "POST /entity-extraction/analyze HTTP/1.1" 200 OK
[2025-08-03 19:10:16][EntityExtractor][background_processor][background] INFO: Java server client initialized with base URL: http://localhost:8080
2025-08-03 19:10:16,662 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:10:16,662 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:10:16,664 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:16,664 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:16,664 INFO sqlalchemy.engine.Engine [generated in 0.00023s] {'analysis_id': 0, 'timestamp': '2025-08-03T19:10:16.582751', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Java server client initialized with base URL: http://localhost:8080", "scrape_request_ref_id": "background", "timestamp": "2025-08-03T19:10:16.582743", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:10:16,664 - sqlalchemy.engine.Engine - INFO - [generated in 0.00023s] {'analysis_id': 0, 'timestamp': '2025-08-03T19:10:16.582751', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Java server client initialized with base URL: http://localhost:8080", "scrape_request_ref_id": "background", "timestamp": "2025-08-03T19:10:16.582743", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:10:16,738 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:10:16,738 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:10:16][EntityExtractor][background_processor][background] INFO: Starting background processing for https://www.shell.in
2025-08-03 19:10:16,889 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:10:16,889 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:10:16,890 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:16,890 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:16,890 INFO sqlalchemy.engine.Engine [cached since 0.2262s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:10:16.841971', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Starting background processing for https://www.shell.in", "scrape_request_ref_id": "background", "timestamp": "2025-08-03T19:10:16.841957", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:10:16,890 - sqlalchemy.engine.Engine - INFO - [cached since 0.2262s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:10:16.841971', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Starting background processing for https://www.shell.in", "scrape_request_ref_id": "background", "timestamp": "2025-08-03T19:10:16.841957", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:10:16,952 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:10:16,952 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:10:17][EntityExtractor][orchestrator_************************************][************************************] INFO: Starting simplified entity extraction orchestration
{
  "scrape_request_ref_id": "************************************",
  "website_url": "https://www.shell.in",
  "org_id": 2
}
2025-08-03 19:10:17,130 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:10:17,130 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:10:17,131 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:17,131 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:17,131 INFO sqlalchemy.engine.Engine [cached since 0.4669s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:10:17.072520', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Starting simplified entity extraction orchestration", "scrape_request_ref_id": "************************************",  ... (20 characters truncated) ... 8-03T19:10:17.072485", "data": {"scrape_request_ref_id": "************************************", "website_url": "https://www.shell.in", "org_id": 2}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:10:17,131 - sqlalchemy.engine.Engine - INFO - [cached since 0.4669s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:10:17.072520', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Starting simplified entity extraction orchestration", "scrape_request_ref_id": "************************************",  ... (20 characters truncated) ... 8-03T19:10:17.072485", "data": {"scrape_request_ref_id": "************************************", "website_url": "https://www.shell.in", "org_id": 2}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:10:17,194 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:10:17,194 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 19:10:17,361 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:10:17,361 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:10:17,365 INFO sqlalchemy.engine.Engine SELECT entity_extraction_analysis.id, entity_extraction_analysis.scrape_request_ref_id, entity_extraction_analysis.website_url, entity_extraction_analysis.processing_status, entity_extraction_analysis.legal_name, entity_extraction_analysis.business_email, entity_extraction_analysis.support_email, entity_extraction_analysis.business_contact_numbers, entity_extraction_analysis.business_location, entity_extraction_analysis.has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details, entity_extraction_analysis.accepts_international_orders, entity_extraction_analysis.shipping_policy_details, entity_extraction_analysis.jurisdiction_place, entity_extraction_analysis.shipping_countries, entity_extraction_analysis.privacy_policy_text, entity_extraction_analysis.terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method, entity_extraction_analysis.total_urls_processed, entity_extraction_analysis.all_urls_found, entity_extraction_analysis.reachable_urls, entity_extraction_analysis.unreachable_urls, entity_extraction_analysis.policy_urls_matched, entity_extraction_analysis.created_at, entity_extraction_analysis.started_at, entity_extraction_analysis.completed_at, entity_extraction_analysis.error_message, entity_extraction_analysis.org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.scrape_request_ref_id = %(scrape_request_ref_id_1)s AND entity_extraction_analysis.org_id = %(org_id_1)s ORDER BY entity_extraction_analysis.id DESC
2025-08-03 19:10:17,365 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_analysis.id, entity_extraction_analysis.scrape_request_ref_id, entity_extraction_analysis.website_url, entity_extraction_analysis.processing_status, entity_extraction_analysis.legal_name, entity_extraction_analysis.business_email, entity_extraction_analysis.support_email, entity_extraction_analysis.business_contact_numbers, entity_extraction_analysis.business_location, entity_extraction_analysis.has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details, entity_extraction_analysis.accepts_international_orders, entity_extraction_analysis.shipping_policy_details, entity_extraction_analysis.jurisdiction_place, entity_extraction_analysis.shipping_countries, entity_extraction_analysis.privacy_policy_text, entity_extraction_analysis.terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method, entity_extraction_analysis.total_urls_processed, entity_extraction_analysis.all_urls_found, entity_extraction_analysis.reachable_urls, entity_extraction_analysis.unreachable_urls, entity_extraction_analysis.policy_urls_matched, entity_extraction_analysis.created_at, entity_extraction_analysis.started_at, entity_extraction_analysis.completed_at, entity_extraction_analysis.error_message, entity_extraction_analysis.org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.scrape_request_ref_id = %(scrape_request_ref_id_1)s AND entity_extraction_analysis.org_id = %(org_id_1)s ORDER BY entity_extraction_analysis.id DESC
2025-08-03 19:10:17,366 INFO sqlalchemy.engine.Engine [generated in 0.00024s] {'scrape_request_ref_id_1': '************************************', 'org_id_1': 2}
2025-08-03 19:10:17,366 - sqlalchemy.engine.Engine - INFO - [generated in 0.00024s] {'scrape_request_ref_id_1': '************************************', 'org_id_1': 2}
[2025-08-03 19:10:17][EntityExtractor][orchestrator_************************************][************************************] INFO: Found existing analysis with ID: 180, status: COMPLETED
2025-08-03 19:10:17,763 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:10:17,763 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:10:17,763 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:17,763 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:17,763 INFO sqlalchemy.engine.Engine [cached since 1.099s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:10:17.438533', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found existing analysis with ID: 180, status: COMPLETED", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:10:17.438525", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:10:17,763 - sqlalchemy.engine.Engine - INFO - [cached since 1.099s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:10:17.438533', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found existing analysis with ID: 180, status: COMPLETED", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:10:17.438525", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:10:17,823 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:10:17,823 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 19:10:17,922 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-03 19:10:17,922 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-03 19:10:18,074 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:10:18,074 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:10:18,074 INFO sqlalchemy.engine.Engine SELECT entity_extraction_analysis.id, entity_extraction_analysis.scrape_request_ref_id, entity_extraction_analysis.website_url, entity_extraction_analysis.processing_status, entity_extraction_analysis.legal_name, entity_extraction_analysis.business_email, entity_extraction_analysis.support_email, entity_extraction_analysis.business_contact_numbers, entity_extraction_analysis.business_location, entity_extraction_analysis.has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details, entity_extraction_analysis.accepts_international_orders, entity_extraction_analysis.shipping_policy_details, entity_extraction_analysis.jurisdiction_place, entity_extraction_analysis.shipping_countries, entity_extraction_analysis.privacy_policy_text, entity_extraction_analysis.terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method, entity_extraction_analysis.total_urls_processed, entity_extraction_analysis.all_urls_found, entity_extraction_analysis.reachable_urls, entity_extraction_analysis.unreachable_urls, entity_extraction_analysis.policy_urls_matched, entity_extraction_analysis.created_at, entity_extraction_analysis.started_at, entity_extraction_analysis.completed_at, entity_extraction_analysis.error_message, entity_extraction_analysis.org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.scrape_request_ref_id = %(scrape_request_ref_id_1)s AND entity_extraction_analysis.org_id = %(org_id_1)s ORDER BY entity_extraction_analysis.id DESC
2025-08-03 19:10:18,074 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_analysis.id, entity_extraction_analysis.scrape_request_ref_id, entity_extraction_analysis.website_url, entity_extraction_analysis.processing_status, entity_extraction_analysis.legal_name, entity_extraction_analysis.business_email, entity_extraction_analysis.support_email, entity_extraction_analysis.business_contact_numbers, entity_extraction_analysis.business_location, entity_extraction_analysis.has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details, entity_extraction_analysis.accepts_international_orders, entity_extraction_analysis.shipping_policy_details, entity_extraction_analysis.jurisdiction_place, entity_extraction_analysis.shipping_countries, entity_extraction_analysis.privacy_policy_text, entity_extraction_analysis.terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method, entity_extraction_analysis.total_urls_processed, entity_extraction_analysis.all_urls_found, entity_extraction_analysis.reachable_urls, entity_extraction_analysis.unreachable_urls, entity_extraction_analysis.policy_urls_matched, entity_extraction_analysis.created_at, entity_extraction_analysis.started_at, entity_extraction_analysis.completed_at, entity_extraction_analysis.error_message, entity_extraction_analysis.org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.scrape_request_ref_id = %(scrape_request_ref_id_1)s AND entity_extraction_analysis.org_id = %(org_id_1)s ORDER BY entity_extraction_analysis.id DESC
2025-08-03 19:10:18,074 INFO sqlalchemy.engine.Engine [cached since 0.7086s ago] {'scrape_request_ref_id_1': '************************************', 'org_id_1': 2}
2025-08-03 19:10:18,074 - sqlalchemy.engine.Engine - INFO - [cached since 0.7086s ago] {'scrape_request_ref_id_1': '************************************', 'org_id_1': 2}
[2025-08-03 19:10:18][EntityExtractor][orchestrator_************************************][************************************] WARNING: Found existing analysis during create - returning existing ID: 180
2025-08-03 19:10:18,234 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:10:18,234 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:10:18,234 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:18,234 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:18,234 INFO sqlalchemy.engine.Engine [cached since 1.57s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:10:18.172881', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "Found existing analysis during create - returning existing ID: 180", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:10:18.172872", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:10:18,234 - sqlalchemy.engine.Engine - INFO - [cached since 1.57s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:10:18.172881', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "Found existing analysis during create - returning existing ID: 180", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:10:18.172872", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:10:18,291 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:10:18,291 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 19:10:18,392 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-03 19:10:18,392 - sqlalchemy.engine.Engine - INFO - ROLLBACK
[2025-08-03 19:10:18][EntityExtractor][180][************************************] INFO: Updated logger with analysis ID: 180
2025-08-03 19:10:18,571 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:10:18,571 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:10:18,572 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:18,572 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:18,572 INFO sqlalchemy.engine.Engine [cached since 1.908s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:10:18.517844', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Updated logger with analysis ID: 180", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:10:18.517834", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:10:18,572 - sqlalchemy.engine.Engine - INFO - [cached since 1.908s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:10:18.517844', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Updated logger with analysis ID: 180", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:10:18.517834", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:10:18,632 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:10:18,632 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 19:10:18,791 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:10:18,791 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:10:18,794 INFO sqlalchemy.engine.Engine SELECT entity_extraction_analysis.id AS entity_extraction_analysis_id, entity_extraction_analysis.scrape_request_ref_id AS entity_extraction_analysis_scrape_request_ref_id, entity_extraction_analysis.website_url AS entity_extraction_analysis_website_url, entity_extraction_analysis.processing_status AS entity_extraction_analysis_processing_status, entity_extraction_analysis.legal_name AS entity_extraction_analysis_legal_name, entity_extraction_analysis.business_email AS entity_extraction_analysis_business_email, entity_extraction_analysis.support_email AS entity_extraction_analysis_support_email, entity_extraction_analysis.business_contact_numbers AS entity_extraction_analysis_business_contact_numbers, entity_extraction_analysis.business_location AS entity_extraction_analysis_business_location, entity_extraction_analysis.has_jurisdiction_law AS entity_extraction_analysis_has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details AS entity_extraction_analysis_jurisdiction_details, entity_extraction_analysis.accepts_international_orders AS entity_extraction_analysis_accepts_international_orders, entity_extraction_analysis.shipping_policy_details AS entity_extraction_analysis_shipping_policy_details, entity_extraction_analysis.jurisdiction_place AS entity_extraction_analysis_jurisdiction_place, entity_extraction_analysis.shipping_countries AS entity_extraction_analysis_shipping_countries, entity_extraction_analysis.privacy_policy_text AS entity_extraction_analysis_privacy_policy_text, entity_extraction_analysis.terms_conditions_text AS entity_extraction_analysis_terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini AS entity_extraction_analysis_urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini AS entity_extraction_analysis_urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method AS entity_extraction_analysis_extraction_method, entity_extraction_analysis.total_urls_processed AS entity_extraction_analysis_total_urls_processed, entity_extraction_analysis.all_urls_found AS entity_extraction_analysis_all_urls_found, entity_extraction_analysis.reachable_urls AS entity_extraction_analysis_reachable_urls, entity_extraction_analysis.unreachable_urls AS entity_extraction_analysis_unreachable_urls, entity_extraction_analysis.policy_urls_matched AS entity_extraction_analysis_policy_urls_matched, entity_extraction_analysis.created_at AS entity_extraction_analysis_created_at, entity_extraction_analysis.started_at AS entity_extraction_analysis_started_at, entity_extraction_analysis.completed_at AS entity_extraction_analysis_completed_at, entity_extraction_analysis.error_message AS entity_extraction_analysis_error_message, entity_extraction_analysis.org_id AS entity_extraction_analysis_org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.id = %(pk_1)s
2025-08-03 19:10:18,794 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_analysis.id AS entity_extraction_analysis_id, entity_extraction_analysis.scrape_request_ref_id AS entity_extraction_analysis_scrape_request_ref_id, entity_extraction_analysis.website_url AS entity_extraction_analysis_website_url, entity_extraction_analysis.processing_status AS entity_extraction_analysis_processing_status, entity_extraction_analysis.legal_name AS entity_extraction_analysis_legal_name, entity_extraction_analysis.business_email AS entity_extraction_analysis_business_email, entity_extraction_analysis.support_email AS entity_extraction_analysis_support_email, entity_extraction_analysis.business_contact_numbers AS entity_extraction_analysis_business_contact_numbers, entity_extraction_analysis.business_location AS entity_extraction_analysis_business_location, entity_extraction_analysis.has_jurisdiction_law AS entity_extraction_analysis_has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details AS entity_extraction_analysis_jurisdiction_details, entity_extraction_analysis.accepts_international_orders AS entity_extraction_analysis_accepts_international_orders, entity_extraction_analysis.shipping_policy_details AS entity_extraction_analysis_shipping_policy_details, entity_extraction_analysis.jurisdiction_place AS entity_extraction_analysis_jurisdiction_place, entity_extraction_analysis.shipping_countries AS entity_extraction_analysis_shipping_countries, entity_extraction_analysis.privacy_policy_text AS entity_extraction_analysis_privacy_policy_text, entity_extraction_analysis.terms_conditions_text AS entity_extraction_analysis_terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini AS entity_extraction_analysis_urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini AS entity_extraction_analysis_urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method AS entity_extraction_analysis_extraction_method, entity_extraction_analysis.total_urls_processed AS entity_extraction_analysis_total_urls_processed, entity_extraction_analysis.all_urls_found AS entity_extraction_analysis_all_urls_found, entity_extraction_analysis.reachable_urls AS entity_extraction_analysis_reachable_urls, entity_extraction_analysis.unreachable_urls AS entity_extraction_analysis_unreachable_urls, entity_extraction_analysis.policy_urls_matched AS entity_extraction_analysis_policy_urls_matched, entity_extraction_analysis.created_at AS entity_extraction_analysis_created_at, entity_extraction_analysis.started_at AS entity_extraction_analysis_started_at, entity_extraction_analysis.completed_at AS entity_extraction_analysis_completed_at, entity_extraction_analysis.error_message AS entity_extraction_analysis_error_message, entity_extraction_analysis.org_id AS entity_extraction_analysis_org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.id = %(pk_1)s
2025-08-03 19:10:18,794 INFO sqlalchemy.engine.Engine [generated in 0.00032s] {'pk_1': 180}
2025-08-03 19:10:18,794 - sqlalchemy.engine.Engine - INFO - [generated in 0.00032s] {'pk_1': 180}
2025-08-03 19:10:18,864 INFO sqlalchemy.engine.Engine UPDATE entity_extraction_analysis SET processing_status=%(processing_status)s, started_at=%(started_at)s WHERE entity_extraction_analysis.id = %(entity_extraction_analysis_id)s
2025-08-03 19:10:18,864 - sqlalchemy.engine.Engine - INFO - UPDATE entity_extraction_analysis SET processing_status=%(processing_status)s, started_at=%(started_at)s WHERE entity_extraction_analysis.id = %(entity_extraction_analysis_id)s
2025-08-03 19:10:18,864 INFO sqlalchemy.engine.Engine [generated in 0.00032s] {'processing_status': 'IN_PROGRESS', 'started_at': '2025-08-03T19:10:18.731087', 'entity_extraction_analysis_id': 180}
2025-08-03 19:10:18,864 - sqlalchemy.engine.Engine - INFO - [generated in 0.00032s] {'processing_status': 'IN_PROGRESS', 'started_at': '2025-08-03T19:10:18.731087', 'entity_extraction_analysis_id': 180}
2025-08-03 19:10:18,923 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:10:18,923 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:10:19][EntityExtractor][180][************************************] INFO: Updated analysis 180 status to IN_PROGRESS
2025-08-03 19:10:19,076 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:10:19,076 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:10:19,076 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:19,076 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:19,076 INFO sqlalchemy.engine.Engine [cached since 2.412s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:10:19.020766', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Updated analysis 180 status to IN_PROGRESS", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:10:19.020757", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:10:19,076 - sqlalchemy.engine.Engine - INFO - [cached since 2.412s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:10:19.020766', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Updated analysis 180 status to IN_PROGRESS", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:10:19.020757", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:10:19,142 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:10:19,142 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:10:19][EntityExtractor][url_retrieval_************************************][************************************] INFO: Retrieving policy URLs with reachability status
2025-08-03 19:10:19,301 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:10:19,301 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:10:19,302 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:19,302 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:19,302 INFO sqlalchemy.engine.Engine [cached since 2.638s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:10:19.241868', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Retrieving policy URLs with reachability status", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:10:19.241851", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:10:19,302 - sqlalchemy.engine.Engine - INFO - [cached since 2.638s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:10:19.241868', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Retrieving policy URLs with reachability status", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:10:19.241851", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:10:19,393 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:10:19,393 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:10:20][EntityExtractor][url_retrieval_************************************][************************************] INFO: Found latest scrape_request_ref_id: ************************************ for domain: shell.in
2025-08-03 19:10:20,400 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:10:20,400 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:10:20,401 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:20,401 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:20,401 INFO sqlalchemy.engine.Engine [cached since 3.737s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:10:20.342712', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found latest scrape_request_ref_id: ************************************ for domain: shell.in", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:10:20.342693", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:10:20,401 - sqlalchemy.engine.Engine - INFO - [cached since 3.737s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:10:20.342712', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found latest scrape_request_ref_id: ************************************ for domain: shell.in", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:10:20.342693", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:10:20,461 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:10:20,461 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:10:20][EntityExtractor][url_retrieval_************************************][************************************] INFO: MCC only provided 0 policy types, force-loading 5 missing policies from policy_analysis_new_gemini
2025-08-03 19:10:20,933 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:10:20,933 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:10:20,934 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:20,934 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:20,934 INFO sqlalchemy.engine.Engine [cached since 4.27s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:10:20.844307', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "MCC only provided 0 policy types, force-loading 5 missing policies from policy_analysis_new_gemini", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:10:20.844297", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:10:20,934 - sqlalchemy.engine.Engine - INFO - [cached since 4.27s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:10:20.844307', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "MCC only provided 0 policy types, force-loading 5 missing policies from policy_analysis_new_gemini", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:10:20.844297", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:10:21,004 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:10:21,004 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:10:21][EntityExtractor][url_retrieval_************************************][************************************] INFO: Found latest scrape_request_ref_id: ************************************ for domain: shell.in
2025-08-03 19:10:21,371 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:10:21,371 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:10:21,372 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:21,372 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:21,372 INFO sqlalchemy.engine.Engine [cached since 4.708s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:10:21.320251', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found latest scrape_request_ref_id: ************************************ for domain: shell.in", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:10:21.320243", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:10:21,372 - sqlalchemy.engine.Engine - INFO - [cached since 4.708s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:10:21.320251', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found latest scrape_request_ref_id: ************************************ for domain: shell.in", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:10:21.320243", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:10:21,431 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:10:21,431 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:10:21][EntityExtractor][url_retrieval_************************************][************************************] INFO: Retrieved policy analysis data for 6 policy types
2025-08-03 19:10:21,854 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:10:21,854 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:10:21,855 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:21,855 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:21,855 INFO sqlalchemy.engine.Engine [cached since 5.191s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:10:21.793392', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Retrieved policy analysis data for 6 policy types", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:10:21.793380", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:10:21,855 - sqlalchemy.engine.Engine - INFO - [cached since 5.191s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:10:21.793392', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Retrieved policy analysis data for 6 policy types", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:10:21.793380", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:10:21,912 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:10:21,912 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:10:22][EntityExtractor][url_retrieval_************************************][************************************] INFO: Force-loaded 5 policies from policy_analysis_new_gemini as unreachable
2025-08-03 19:10:22,188 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:10:22,188 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:10:22,188 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:22,188 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:22,188 INFO sqlalchemy.engine.Engine [cached since 5.524s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:10:22.131191', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Force-loaded 5 policies from policy_analysis_new_gemini as unreachable", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:10:22.131182", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:10:22,188 - sqlalchemy.engine.Engine - INFO - [cached since 5.524s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:10:22.131191', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Force-loaded 5 policies from policy_analysis_new_gemini as unreachable", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:10:22.131182", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:10:22,259 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:10:22,259 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:10:22][EntityExtractor][url_retrieval_************************************][************************************] INFO: Total policy URLs retrieved: 5
2025-08-03 19:10:22,411 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:10:22,411 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:10:22,412 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:22,412 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:22,412 INFO sqlalchemy.engine.Engine [cached since 5.748s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:10:22.361836', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Total policy URLs retrieved: 5", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:10:22.361827", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:10:22,412 - sqlalchemy.engine.Engine - INFO - [cached since 5.748s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:10:22.361836', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Total policy URLs retrieved: 5", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:10:22.361827", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:10:22,472 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:10:22,472 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:10:22][EntityExtractor][url_retrieval_************************************][************************************] INFO: Found latest scrape_request_ref_id: ************************************ for domain: shell.in
2025-08-03 19:10:22,831 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:10:22,831 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:10:22,832 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:22,832 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:22,832 INFO sqlalchemy.engine.Engine [cached since 6.168s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:10:22.780942', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found latest scrape_request_ref_id: ************************************ for domain: shell.in", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:10:22.780929", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:10:22,832 - sqlalchemy.engine.Engine - INFO - [cached since 6.168s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:10:22.780942', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found latest scrape_request_ref_id: ************************************ for domain: shell.in", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:10:22.780929", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:10:22,903 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:10:22,903 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:10:23][EntityExtractor][url_retrieval_************************************][************************************] INFO: Retrieved policy analysis data for 6 policy types
2025-08-03 19:10:23,316 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:10:23,316 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:10:23,316 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:23,316 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:23,316 INFO sqlalchemy.engine.Engine [cached since 6.653s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:10:23.245239', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Retrieved policy analysis data for 6 policy types", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:10:23.245224", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:10:23,316 - sqlalchemy.engine.Engine - INFO - [cached since 6.653s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:10:23.245239', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Retrieved policy analysis data for 6 policy types", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:10:23.245224", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:10:23,390 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:10:23,390 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:10:23][EntityExtractor][180][************************************] INFO: Filtered policy URLs keys: ['privacy_policy', 'terms_and_condition', 'shipping_delivery', 'contact_us', 'about_us']
2025-08-03 19:10:23,661 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:10:23,661 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:10:23,662 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:23,662 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:23,662 INFO sqlalchemy.engine.Engine [cached since 6.998s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:10:23.612997', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Filtered policy URLs keys: [\'privacy_policy\', \'terms_and_condition\', \'shipping_delivery\', \'contact_us\', \'about_us\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:10:23.612985", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:10:23,662 - sqlalchemy.engine.Engine - INFO - [cached since 6.998s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:10:23.612997', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Filtered policy URLs keys: [\'privacy_policy\', \'terms_and_condition\', \'shipping_delivery\', \'contact_us\', \'about_us\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:10:23.612985", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:10:23,730 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:10:23,730 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:10:23][EntityExtractor][180][************************************] INFO: Extracted text length for privacy_policy: 2340
2025-08-03 19:10:23,895 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:10:23,895 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:10:23,895 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:23,895 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:23,896 INFO sqlalchemy.engine.Engine [cached since 7.232s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:10:23.841028', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Extracted text length for privacy_policy: 2340", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:10:23.841018", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:10:23,896 - sqlalchemy.engine.Engine - INFO - [cached since 7.232s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:10:23.841028', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Extracted text length for privacy_policy: 2340", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:10:23.841018", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:10:23,975 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:10:23,975 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:10:24][EntityExtractor][180][************************************] INFO: Extracted text length for terms_and_condition: 1479
2025-08-03 19:10:24,170 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:10:24,170 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:10:24,171 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:24,171 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:24,171 INFO sqlalchemy.engine.Engine [cached since 7.507s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:10:24.122034', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Extracted text length for terms_and_condition: 1479", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:10:24.122020", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:10:24,171 - sqlalchemy.engine.Engine - INFO - [cached since 7.507s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:10:24.122034', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Extracted text length for terms_and_condition: 1479", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:10:24.122020", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:10:24,232 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:10:24,232 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:10:24][EntityExtractor][url_retrieval_************************************][************************************] INFO: Checking Gemini reachability for 5 URLs
2025-08-03 19:10:24,383 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:10:24,383 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:10:24,383 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:24,383 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:24,383 INFO sqlalchemy.engine.Engine [cached since 7.719s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:10:24.331984', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Checking Gemini reachability for 5 URLs", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:10:24.331975", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:10:24,383 - sqlalchemy.engine.Engine - INFO - [cached since 7.719s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:10:24.331984', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Checking Gemini reachability for 5 URLs", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:10:24.331975", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:10:24,442 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:10:24,442 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:10:24][EntityExtractor][url_retrieval_************************************][************************************] INFO: Gemini reachability check completed: 5 reachable, 0 unreachable
2025-08-03 19:10:24,601 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:10:24,601 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:10:24,602 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:24,602 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:24,602 INFO sqlalchemy.engine.Engine [cached since 7.938s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:10:24.546149', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Gemini reachability check completed: 5 reachable, 0 unreachable", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:10:24.546140", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:10:24,602 - sqlalchemy.engine.Engine - INFO - [cached since 7.938s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:10:24.546149', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Gemini reachability check completed: 5 reachable, 0 unreachable", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:10:24.546140", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:10:24,662 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:10:24,662 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 19:10:27,803 - google_genai.models - INFO - AFC is enabled with max remote calls: 20000.
2025-08-03 19:10:51,087 - httpx - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent "HTTP/1.1 200 OK"
2025-08-03 19:10:51,088 - google_genai.models - INFO - AFC remote call 1 is done.
[2025-08-03 19:10:27][legacy_unknown][unknown] INFO: Starting Gemini API call
{
  "model": "gemini-2.5-flash",
  "timeout": 120,
  "max_retries": 3,
  "prompt_length": 5395,
  "context": {
    "task_type": "legacy"
  }
}
[2025-08-03 19:10:27][legacy_unknown][unknown] INFO: Gemini API attempt 1/3
[2025-08-03 19:10:51][legacy_unknown][unknown] INFO: Gemini API Usage: cache_tokens_details=None cached_content_token_count=None candidates_token_count=363 candidates_tokens_details=None prompt_token_count=1281 prompt_tokens_details=[ModalityTokenCount(
  modality=<MediaModality.TEXT: 'TEXT'>,
  token_count=1281
)] thoughts_token_count=2475 tool_use_prompt_token_count=None tool_use_prompt_tokens_details=None total_token_count=4119 traffic_type=None
[2025-08-03 19:10:51][legacy_unknown][unknown] INFO: Gemini API call successful
{
  "attempt": 1,
  "response_length": 751,
  "finish_reason": "STOP"
}
[2025-08-03 19:10:53][api_response_logger][unknown] INFO: Gemini API Call - Model: gemini-2.5-flash, Request ID: None
{
  "response_preview": "```json\n{\n    \"legal_name\": \"Shell India Markets Private Limited\",\n    \"business_email\": [\n        \"<EMAIL>\"\n    ],\n    \"support_email\": [],\n    \"business_contact_numbers\": [\n        \"044-3099 1103\",\n        \"044-4344 2650\",\n        \"+91 44 46945101\"\n    ],\n    \"business_location\": [\n        \"2nd Floor, Campus 4A, RMZ Millenia Business Park, 143, Dr MGR Road, Perungudi, Chennai - 600 096, India\",\n        \"Commerzone, Block-2, No.2, 200 Feet Radial Road, Pallikaranai, Chennai \u2013 600100, India\"\n    ],\n    \"accepts_international_orders\": \"not_mentioned\",\n    \"shipping_countries\": [],\n    \"shipping_policy_details\": \"\",\n    \"has_jurisdiction_law\": \"yes\",\n    \"jurisdiction_place\": [],\n    \"jurisdiction_details\": \"\"\n}\n```"
}
[2025-08-03 19:10:53][api_response_logger][unknown] INFO: Gemini API Call - Model: gemini-2.5-flash, Request ID: ************************************
{
  "response_preview": "```json\n{\n    \"legal_name\": \"Shell India Markets Private Limited\",\n    \"business_email\": [\n        \"<EMAIL>\"\n    ],\n    \"support_email\": [],\n    \"business_contact_numbers\": [\n        \"044-3099 1103\",\n        \"044-4344 2650\",\n        \"+91 44 46945101\"\n    ],\n    \"business_location\": [\n        \"2nd Floor, Campus 4A, RMZ Millenia Business Park, 143, Dr MGR Road, Perungudi, Chennai - 600 096, India\",\n        \"Commerzone, Block-2, No.2, 200 Feet Radial Road, Pallikaranai, Chennai \u2013 600100, India\"\n    ],\n    \"accepts_international_orders\": \"not_mentioned\",\n    \"shipping_countries\": [],\n    \"shipping_policy_details\": \"\",\n    \"has_jurisdiction_law\": \"yes\",\n    \"jurisdiction_place\": [],\n    \"jurisdiction_details\": \"\"\n}\n```"
}
2025-08-03 19:10:53,142 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:10:53,142 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:10:53,146 INFO sqlalchemy.engine.Engine SELECT entity_extraction_url_analysis.id, entity_extraction_url_analysis.analysis_id, entity_extraction_url_analysis.entity_analysis_id, entity_extraction_url_analysis.scrape_request_ref_id, entity_extraction_url_analysis.url, entity_extraction_url_analysis.url_type, entity_extraction_url_analysis.url_classification, entity_extraction_url_analysis.url_source_table, entity_extraction_url_analysis.is_reachable_by_gemini, entity_extraction_url_analysis.soft_classification, entity_extraction_url_analysis.hard_classification, entity_extraction_url_analysis.extracted_text, entity_extraction_url_analysis.extracted_text_length, entity_extraction_url_analysis.extracted_entities, entity_extraction_url_analysis.processing_method, entity_extraction_url_analysis.processing_status, entity_extraction_url_analysis.error_message, entity_extraction_url_analysis.created_at, entity_extraction_url_analysis.processed_at, entity_extraction_url_analysis.org_id 
FROM entity_extraction_url_analysis 
WHERE entity_extraction_url_analysis.analysis_id = %(analysis_id_1)s AND entity_extraction_url_analysis.url = %(url_1)s
2025-08-03 19:10:53,146 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_url_analysis.id, entity_extraction_url_analysis.analysis_id, entity_extraction_url_analysis.entity_analysis_id, entity_extraction_url_analysis.scrape_request_ref_id, entity_extraction_url_analysis.url, entity_extraction_url_analysis.url_type, entity_extraction_url_analysis.url_classification, entity_extraction_url_analysis.url_source_table, entity_extraction_url_analysis.is_reachable_by_gemini, entity_extraction_url_analysis.soft_classification, entity_extraction_url_analysis.hard_classification, entity_extraction_url_analysis.extracted_text, entity_extraction_url_analysis.extracted_text_length, entity_extraction_url_analysis.extracted_entities, entity_extraction_url_analysis.processing_method, entity_extraction_url_analysis.processing_status, entity_extraction_url_analysis.error_message, entity_extraction_url_analysis.created_at, entity_extraction_url_analysis.processed_at, entity_extraction_url_analysis.org_id 
FROM entity_extraction_url_analysis 
WHERE entity_extraction_url_analysis.analysis_id = %(analysis_id_1)s AND entity_extraction_url_analysis.url = %(url_1)s
2025-08-03 19:10:53,146 INFO sqlalchemy.engine.Engine [generated in 0.00019s] {'analysis_id_1': 180, 'url_1': 'https://www.shell.in/privacy.html'}
2025-08-03 19:10:53,146 - sqlalchemy.engine.Engine - INFO - [generated in 0.00019s] {'analysis_id_1': 180, 'url_1': 'https://www.shell.in/privacy.html'}
[2025-08-03 19:10:53][EntityExtractor][None][************************************] WARNING: URL analysis record not found for URL: https://www.shell.in/privacy.html
2025-08-03 19:10:53,251 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:10:53,251 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:10:53,251 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:53,251 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:53,251 INFO sqlalchemy.engine.Engine [cached since 36.59s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:10:53.205871', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "URL analysis record not found for URL: https://www.shell.in/privacy.html", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:10:53.205850", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:10:53,251 - sqlalchemy.engine.Engine - INFO - [cached since 36.59s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:10:53.205871', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "URL analysis record not found for URL: https://www.shell.in/privacy.html", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:10:53.205850", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:10:53,314 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:10:53,314 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 19:10:53,503 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-03 19:10:53,503 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-03 19:10:53,796 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:10:53,796 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:10:53,797 INFO sqlalchemy.engine.Engine SELECT entity_extraction_url_analysis.id, entity_extraction_url_analysis.analysis_id, entity_extraction_url_analysis.entity_analysis_id, entity_extraction_url_analysis.scrape_request_ref_id, entity_extraction_url_analysis.url, entity_extraction_url_analysis.url_type, entity_extraction_url_analysis.url_classification, entity_extraction_url_analysis.url_source_table, entity_extraction_url_analysis.is_reachable_by_gemini, entity_extraction_url_analysis.soft_classification, entity_extraction_url_analysis.hard_classification, entity_extraction_url_analysis.extracted_text, entity_extraction_url_analysis.extracted_text_length, entity_extraction_url_analysis.extracted_entities, entity_extraction_url_analysis.processing_method, entity_extraction_url_analysis.processing_status, entity_extraction_url_analysis.error_message, entity_extraction_url_analysis.created_at, entity_extraction_url_analysis.processed_at, entity_extraction_url_analysis.org_id 
FROM entity_extraction_url_analysis 
WHERE entity_extraction_url_analysis.analysis_id = %(analysis_id_1)s AND entity_extraction_url_analysis.url = %(url_1)s
2025-08-03 19:10:53,797 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_url_analysis.id, entity_extraction_url_analysis.analysis_id, entity_extraction_url_analysis.entity_analysis_id, entity_extraction_url_analysis.scrape_request_ref_id, entity_extraction_url_analysis.url, entity_extraction_url_analysis.url_type, entity_extraction_url_analysis.url_classification, entity_extraction_url_analysis.url_source_table, entity_extraction_url_analysis.is_reachable_by_gemini, entity_extraction_url_analysis.soft_classification, entity_extraction_url_analysis.hard_classification, entity_extraction_url_analysis.extracted_text, entity_extraction_url_analysis.extracted_text_length, entity_extraction_url_analysis.extracted_entities, entity_extraction_url_analysis.processing_method, entity_extraction_url_analysis.processing_status, entity_extraction_url_analysis.error_message, entity_extraction_url_analysis.created_at, entity_extraction_url_analysis.processed_at, entity_extraction_url_analysis.org_id 
FROM entity_extraction_url_analysis 
WHERE entity_extraction_url_analysis.analysis_id = %(analysis_id_1)s AND entity_extraction_url_analysis.url = %(url_1)s
2025-08-03 19:10:53,797 INFO sqlalchemy.engine.Engine [cached since 0.6514s ago] {'analysis_id_1': 180, 'url_1': 'https://www.shell.in/terms-and-conditions.html'}
2025-08-03 19:10:53,797 - sqlalchemy.engine.Engine - INFO - [cached since 0.6514s ago] {'analysis_id_1': 180, 'url_1': 'https://www.shell.in/terms-and-conditions.html'}
[2025-08-03 19:10:53][EntityExtractor][None][************************************] WARNING: URL analysis record not found for URL: https://www.shell.in/terms-and-conditions.html
2025-08-03 19:10:53,953 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:10:53,953 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:10:53,954 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:53,954 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:53,954 INFO sqlalchemy.engine.Engine [cached since 37.29s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:10:53.879024', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "URL analysis record not found for URL: https://www.shell.in/terms-and-conditions.html", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:10:53.879017", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:10:53,954 - sqlalchemy.engine.Engine - INFO - [cached since 37.29s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:10:53.879024', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "URL analysis record not found for URL: https://www.shell.in/terms-and-conditions.html", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:10:53.879017", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:10:54,036 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:10:54,036 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 19:10:54,159 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-03 19:10:54,159 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-03 19:10:54,338 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:10:54,338 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:10:54,338 INFO sqlalchemy.engine.Engine SELECT entity_extraction_url_analysis.id, entity_extraction_url_analysis.analysis_id, entity_extraction_url_analysis.entity_analysis_id, entity_extraction_url_analysis.scrape_request_ref_id, entity_extraction_url_analysis.url, entity_extraction_url_analysis.url_type, entity_extraction_url_analysis.url_classification, entity_extraction_url_analysis.url_source_table, entity_extraction_url_analysis.is_reachable_by_gemini, entity_extraction_url_analysis.soft_classification, entity_extraction_url_analysis.hard_classification, entity_extraction_url_analysis.extracted_text, entity_extraction_url_analysis.extracted_text_length, entity_extraction_url_analysis.extracted_entities, entity_extraction_url_analysis.processing_method, entity_extraction_url_analysis.processing_status, entity_extraction_url_analysis.error_message, entity_extraction_url_analysis.created_at, entity_extraction_url_analysis.processed_at, entity_extraction_url_analysis.org_id 
FROM entity_extraction_url_analysis 
WHERE entity_extraction_url_analysis.analysis_id = %(analysis_id_1)s AND entity_extraction_url_analysis.url = %(url_1)s
2025-08-03 19:10:54,338 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_url_analysis.id, entity_extraction_url_analysis.analysis_id, entity_extraction_url_analysis.entity_analysis_id, entity_extraction_url_analysis.scrape_request_ref_id, entity_extraction_url_analysis.url, entity_extraction_url_analysis.url_type, entity_extraction_url_analysis.url_classification, entity_extraction_url_analysis.url_source_table, entity_extraction_url_analysis.is_reachable_by_gemini, entity_extraction_url_analysis.soft_classification, entity_extraction_url_analysis.hard_classification, entity_extraction_url_analysis.extracted_text, entity_extraction_url_analysis.extracted_text_length, entity_extraction_url_analysis.extracted_entities, entity_extraction_url_analysis.processing_method, entity_extraction_url_analysis.processing_status, entity_extraction_url_analysis.error_message, entity_extraction_url_analysis.created_at, entity_extraction_url_analysis.processed_at, entity_extraction_url_analysis.org_id 
FROM entity_extraction_url_analysis 
WHERE entity_extraction_url_analysis.analysis_id = %(analysis_id_1)s AND entity_extraction_url_analysis.url = %(url_1)s
2025-08-03 19:10:54,338 INFO sqlalchemy.engine.Engine [cached since 1.193s ago] {'analysis_id_1': 180, 'url_1': 'not_found'}
2025-08-03 19:10:54,338 - sqlalchemy.engine.Engine - INFO - [cached since 1.193s ago] {'analysis_id_1': 180, 'url_1': 'not_found'}
[2025-08-03 19:10:54][EntityExtractor][None][************************************] WARNING: URL analysis record not found for URL: not_found
2025-08-03 19:10:54,470 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:10:54,470 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:10:54,470 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:54,470 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:54,471 INFO sqlalchemy.engine.Engine [cached since 37.81s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:10:54.416957', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "URL analysis record not found for URL: not_found", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:10:54.416951", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:10:54,471 - sqlalchemy.engine.Engine - INFO - [cached since 37.81s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:10:54.416957', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "URL analysis record not found for URL: not_found", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:10:54.416951", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:10:54,531 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:10:54,531 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 19:10:54,658 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-03 19:10:54,658 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-03 19:10:54,830 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:10:54,830 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:10:54,830 INFO sqlalchemy.engine.Engine SELECT entity_extraction_url_analysis.id, entity_extraction_url_analysis.analysis_id, entity_extraction_url_analysis.entity_analysis_id, entity_extraction_url_analysis.scrape_request_ref_id, entity_extraction_url_analysis.url, entity_extraction_url_analysis.url_type, entity_extraction_url_analysis.url_classification, entity_extraction_url_analysis.url_source_table, entity_extraction_url_analysis.is_reachable_by_gemini, entity_extraction_url_analysis.soft_classification, entity_extraction_url_analysis.hard_classification, entity_extraction_url_analysis.extracted_text, entity_extraction_url_analysis.extracted_text_length, entity_extraction_url_analysis.extracted_entities, entity_extraction_url_analysis.processing_method, entity_extraction_url_analysis.processing_status, entity_extraction_url_analysis.error_message, entity_extraction_url_analysis.created_at, entity_extraction_url_analysis.processed_at, entity_extraction_url_analysis.org_id 
FROM entity_extraction_url_analysis 
WHERE entity_extraction_url_analysis.analysis_id = %(analysis_id_1)s AND entity_extraction_url_analysis.url = %(url_1)s
2025-08-03 19:10:54,830 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_url_analysis.id, entity_extraction_url_analysis.analysis_id, entity_extraction_url_analysis.entity_analysis_id, entity_extraction_url_analysis.scrape_request_ref_id, entity_extraction_url_analysis.url, entity_extraction_url_analysis.url_type, entity_extraction_url_analysis.url_classification, entity_extraction_url_analysis.url_source_table, entity_extraction_url_analysis.is_reachable_by_gemini, entity_extraction_url_analysis.soft_classification, entity_extraction_url_analysis.hard_classification, entity_extraction_url_analysis.extracted_text, entity_extraction_url_analysis.extracted_text_length, entity_extraction_url_analysis.extracted_entities, entity_extraction_url_analysis.processing_method, entity_extraction_url_analysis.processing_status, entity_extraction_url_analysis.error_message, entity_extraction_url_analysis.created_at, entity_extraction_url_analysis.processed_at, entity_extraction_url_analysis.org_id 
FROM entity_extraction_url_analysis 
WHERE entity_extraction_url_analysis.analysis_id = %(analysis_id_1)s AND entity_extraction_url_analysis.url = %(url_1)s
2025-08-03 19:10:54,830 INFO sqlalchemy.engine.Engine [cached since 1.685s ago] {'analysis_id_1': 180, 'url_1': 'https://www.shell.in/about-us/contact-us.html'}
2025-08-03 19:10:54,830 - sqlalchemy.engine.Engine - INFO - [cached since 1.685s ago] {'analysis_id_1': 180, 'url_1': 'https://www.shell.in/about-us/contact-us.html'}
[2025-08-03 19:10:54][EntityExtractor][None][************************************] WARNING: URL analysis record not found for URL: https://www.shell.in/about-us/contact-us.html
2025-08-03 19:10:55,000 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:10:55,000 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:10:55,000 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:55,000 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:55,000 INFO sqlalchemy.engine.Engine [cached since 38.34s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:10:54.930650', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "URL analysis record not found for URL: https://www.shell.in/about-us/contact-us.html", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:10:54.930644", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:10:55,000 - sqlalchemy.engine.Engine - INFO - [cached since 38.34s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:10:54.930650', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "URL analysis record not found for URL: https://www.shell.in/about-us/contact-us.html", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:10:54.930644", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:10:55,062 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:10:55,062 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 19:10:55,162 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-03 19:10:55,162 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-03 19:10:55,310 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:10:55,310 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:10:55,311 INFO sqlalchemy.engine.Engine SELECT entity_extraction_url_analysis.id, entity_extraction_url_analysis.analysis_id, entity_extraction_url_analysis.entity_analysis_id, entity_extraction_url_analysis.scrape_request_ref_id, entity_extraction_url_analysis.url, entity_extraction_url_analysis.url_type, entity_extraction_url_analysis.url_classification, entity_extraction_url_analysis.url_source_table, entity_extraction_url_analysis.is_reachable_by_gemini, entity_extraction_url_analysis.soft_classification, entity_extraction_url_analysis.hard_classification, entity_extraction_url_analysis.extracted_text, entity_extraction_url_analysis.extracted_text_length, entity_extraction_url_analysis.extracted_entities, entity_extraction_url_analysis.processing_method, entity_extraction_url_analysis.processing_status, entity_extraction_url_analysis.error_message, entity_extraction_url_analysis.created_at, entity_extraction_url_analysis.processed_at, entity_extraction_url_analysis.org_id 
FROM entity_extraction_url_analysis 
WHERE entity_extraction_url_analysis.analysis_id = %(analysis_id_1)s AND entity_extraction_url_analysis.url = %(url_1)s
2025-08-03 19:10:55,311 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_url_analysis.id, entity_extraction_url_analysis.analysis_id, entity_extraction_url_analysis.entity_analysis_id, entity_extraction_url_analysis.scrape_request_ref_id, entity_extraction_url_analysis.url, entity_extraction_url_analysis.url_type, entity_extraction_url_analysis.url_classification, entity_extraction_url_analysis.url_source_table, entity_extraction_url_analysis.is_reachable_by_gemini, entity_extraction_url_analysis.soft_classification, entity_extraction_url_analysis.hard_classification, entity_extraction_url_analysis.extracted_text, entity_extraction_url_analysis.extracted_text_length, entity_extraction_url_analysis.extracted_entities, entity_extraction_url_analysis.processing_method, entity_extraction_url_analysis.processing_status, entity_extraction_url_analysis.error_message, entity_extraction_url_analysis.created_at, entity_extraction_url_analysis.processed_at, entity_extraction_url_analysis.org_id 
FROM entity_extraction_url_analysis 
WHERE entity_extraction_url_analysis.analysis_id = %(analysis_id_1)s AND entity_extraction_url_analysis.url = %(url_1)s
2025-08-03 19:10:55,311 INFO sqlalchemy.engine.Engine [cached since 2.165s ago] {'analysis_id_1': 180, 'url_1': 'https://www.shell.in/about-us/careers.html'}
2025-08-03 19:10:55,311 - sqlalchemy.engine.Engine - INFO - [cached since 2.165s ago] {'analysis_id_1': 180, 'url_1': 'https://www.shell.in/about-us/careers.html'}
[2025-08-03 19:10:55][EntityExtractor][None][************************************] WARNING: URL analysis record not found for URL: https://www.shell.in/about-us/careers.html
2025-08-03 19:10:55,421 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:10:55,421 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:10:55,422 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:55,422 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:55,422 INFO sqlalchemy.engine.Engine [cached since 38.76s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:10:55.376112', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "URL analysis record not found for URL: https://www.shell.in/about-us/careers.html", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:10:55.376106", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:10:55,422 - sqlalchemy.engine.Engine - INFO - [cached since 38.76s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:10:55.376112', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "URL analysis record not found for URL: https://www.shell.in/about-us/careers.html", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:10:55.376106", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:10:55,486 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:10:55,486 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 19:10:55,601 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-03 19:10:55,601 - sqlalchemy.engine.Engine - INFO - ROLLBACK
[2025-08-03 19:10:55][EntityExtractor][180][************************************] INFO: Gemini extraction rate 54.5% < 90%, forcing backup flow for all fields
2025-08-03 19:10:55,752 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:10:55,752 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:10:55,752 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:55,752 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:55,753 INFO sqlalchemy.engine.Engine [cached since 39.09s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:10:55.701809', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Gemini extraction rate 54.5% < 90%, forcing backup flow for all fields", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:10:55.701801", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:10:55,753 - sqlalchemy.engine.Engine - INFO - [cached since 39.09s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:10:55.701809', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Gemini extraction rate 54.5% < 90%, forcing backup flow for all fields", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:10:55.701801", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:10:55,819 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:10:55,819 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:10:55][EntityExtractor][180][************************************] INFO: Gemini missing fields to fallback: ['support_email', 'shipping_countries', 'shipping_policy_details', 'jurisdiction_place', 'jurisdiction_details']
2025-08-03 19:10:55,971 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:10:55,971 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:10:55,971 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:55,971 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:55,971 INFO sqlalchemy.engine.Engine [cached since 39.31s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:10:55.923796', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Gemini missing fields to fallback: [\'support_email\', \'shipping_countries\', \'shipping_policy_details\', \'jurisdict ... (11 characters truncated) ... , \'jurisdiction_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:10:55.923788", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:10:55,971 - sqlalchemy.engine.Engine - INFO - [cached since 39.31s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:10:55.923796', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Gemini missing fields to fallback: [\'support_email\', \'shipping_countries\', \'shipping_policy_details\', \'jurisdict ... (11 characters truncated) ... , \'jurisdiction_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:10:55.923788", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:10:56,032 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:10:56,032 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:10:56][EntityExtractor][180][************************************] INFO: Starting OpenAI backup flow for missing fields: ['support_email', 'shipping_countries', 'shipping_policy_details', 'jurisdiction_place', 'jurisdiction_details']
2025-08-03 19:10:56,192 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:10:56,192 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:10:56,192 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:56,192 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:56,192 INFO sqlalchemy.engine.Engine [cached since 39.53s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:10:56.141295', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Starting OpenAI backup flow for missing fields: [\'support_email\', \'shipping_countries\', \'shipping_policy_details\' ... (24 characters truncated) ... , \'jurisdiction_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:10:56.141282", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:10:56,192 - sqlalchemy.engine.Engine - INFO - [cached since 39.53s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:10:56.141295', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Starting OpenAI backup flow for missing fields: [\'support_email\', \'shipping_countries\', \'shipping_policy_details\' ... (24 characters truncated) ... , \'jurisdiction_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:10:56.141282", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:10:56,264 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:10:56,264 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 19:10:56,466 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:10:56,466 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:10:56,468 INFO sqlalchemy.engine.Engine SELECT policy_analysis_new_gemini.id, policy_analysis_new_gemini.website, policy_analysis_new_gemini.scrape_request_ref_id, policy_analysis_new_gemini.analysis_flow_used, policy_analysis_new_gemini.reachability_percentage, policy_analysis_new_gemini.total_urls_processed, policy_analysis_new_gemini.privacy_policy_url, policy_analysis_new_gemini.terms_and_condition_url, policy_analysis_new_gemini.shipping_delivery_url, policy_analysis_new_gemini.returns_cancellation_exchange_url, policy_analysis_new_gemini.contact_us_url, policy_analysis_new_gemini.home_page_url, policy_analysis_new_gemini.about_us_url, policy_analysis_new_gemini.privacy_policy_text, policy_analysis_new_gemini.terms_and_condition_text, policy_analysis_new_gemini.shipping_delivery_text, policy_analysis_new_gemini.returns_cancellation_exchange_text, policy_analysis_new_gemini.contact_us_text, policy_analysis_new_gemini.home_page_text, policy_analysis_new_gemini.about_us_text, policy_analysis_new_gemini.privacy_policy_screenshot, policy_analysis_new_gemini.terms_and_condition_screenshot, policy_analysis_new_gemini.shipping_delivery_screenshot, policy_analysis_new_gemini.returns_cancellation_exchange_screenshot, policy_analysis_new_gemini.contact_us_screenshot, policy_analysis_new_gemini.home_page_screenshot, policy_analysis_new_gemini.about_us_screenshot, policy_analysis_new_gemini.instagram_url, policy_analysis_new_gemini.instagram_text, policy_analysis_new_gemini.instagram_screenshot, policy_analysis_new_gemini.youtube_url, policy_analysis_new_gemini.youtube_text, policy_analysis_new_gemini.youtube_screenshot, policy_analysis_new_gemini.facebook_url, policy_analysis_new_gemini.facebook_text, policy_analysis_new_gemini.facebook_screenshot, policy_analysis_new_gemini.twitter_url, policy_analysis_new_gemini.twitter_text, policy_analysis_new_gemini.twitter_screenshot, policy_analysis_new_gemini.linkedin_url, policy_analysis_new_gemini.linkedin_text, policy_analysis_new_gemini.linkedin_screenshot, policy_analysis_new_gemini.pinterest_url, policy_analysis_new_gemini.pinterest_text, policy_analysis_new_gemini.pinterest_screenshot, policy_analysis_new_gemini.x_url, policy_analysis_new_gemini.x_text, policy_analysis_new_gemini.x_screenshot, policy_analysis_new_gemini.result_status, policy_analysis_new_gemini.failed_at, policy_analysis_new_gemini.last_updated, policy_analysis_new_gemini.details, policy_analysis_new_gemini.processing_status, policy_analysis_new_gemini.created_at, policy_analysis_new_gemini.started_at, policy_analysis_new_gemini.completed_at, policy_analysis_new_gemini.error_message, policy_analysis_new_gemini.org_id 
FROM policy_analysis_new_gemini 
WHERE policy_analysis_new_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s ORDER BY policy_analysis_new_gemini.id DESC
2025-08-03 19:10:56,468 - sqlalchemy.engine.Engine - INFO - SELECT policy_analysis_new_gemini.id, policy_analysis_new_gemini.website, policy_analysis_new_gemini.scrape_request_ref_id, policy_analysis_new_gemini.analysis_flow_used, policy_analysis_new_gemini.reachability_percentage, policy_analysis_new_gemini.total_urls_processed, policy_analysis_new_gemini.privacy_policy_url, policy_analysis_new_gemini.terms_and_condition_url, policy_analysis_new_gemini.shipping_delivery_url, policy_analysis_new_gemini.returns_cancellation_exchange_url, policy_analysis_new_gemini.contact_us_url, policy_analysis_new_gemini.home_page_url, policy_analysis_new_gemini.about_us_url, policy_analysis_new_gemini.privacy_policy_text, policy_analysis_new_gemini.terms_and_condition_text, policy_analysis_new_gemini.shipping_delivery_text, policy_analysis_new_gemini.returns_cancellation_exchange_text, policy_analysis_new_gemini.contact_us_text, policy_analysis_new_gemini.home_page_text, policy_analysis_new_gemini.about_us_text, policy_analysis_new_gemini.privacy_policy_screenshot, policy_analysis_new_gemini.terms_and_condition_screenshot, policy_analysis_new_gemini.shipping_delivery_screenshot, policy_analysis_new_gemini.returns_cancellation_exchange_screenshot, policy_analysis_new_gemini.contact_us_screenshot, policy_analysis_new_gemini.home_page_screenshot, policy_analysis_new_gemini.about_us_screenshot, policy_analysis_new_gemini.instagram_url, policy_analysis_new_gemini.instagram_text, policy_analysis_new_gemini.instagram_screenshot, policy_analysis_new_gemini.youtube_url, policy_analysis_new_gemini.youtube_text, policy_analysis_new_gemini.youtube_screenshot, policy_analysis_new_gemini.facebook_url, policy_analysis_new_gemini.facebook_text, policy_analysis_new_gemini.facebook_screenshot, policy_analysis_new_gemini.twitter_url, policy_analysis_new_gemini.twitter_text, policy_analysis_new_gemini.twitter_screenshot, policy_analysis_new_gemini.linkedin_url, policy_analysis_new_gemini.linkedin_text, policy_analysis_new_gemini.linkedin_screenshot, policy_analysis_new_gemini.pinterest_url, policy_analysis_new_gemini.pinterest_text, policy_analysis_new_gemini.pinterest_screenshot, policy_analysis_new_gemini.x_url, policy_analysis_new_gemini.x_text, policy_analysis_new_gemini.x_screenshot, policy_analysis_new_gemini.result_status, policy_analysis_new_gemini.failed_at, policy_analysis_new_gemini.last_updated, policy_analysis_new_gemini.details, policy_analysis_new_gemini.processing_status, policy_analysis_new_gemini.created_at, policy_analysis_new_gemini.started_at, policy_analysis_new_gemini.completed_at, policy_analysis_new_gemini.error_message, policy_analysis_new_gemini.org_id 
FROM policy_analysis_new_gemini 
WHERE policy_analysis_new_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s ORDER BY policy_analysis_new_gemini.id DESC
2025-08-03 19:10:56,469 INFO sqlalchemy.engine.Engine [generated in 0.00027s] {'scrape_request_ref_id_1': '************************************'}
2025-08-03 19:10:56,469 - sqlalchemy.engine.Engine - INFO - [generated in 0.00027s] {'scrape_request_ref_id_1': '************************************'}
[2025-08-03 19:10:56][EntityExtractor][180][************************************] INFO: Retrieved policy texts: ['privacy_policy', 'terms_and_condition', 'contact_us', 'about_us']
2025-08-03 19:10:56,689 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:10:56,689 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:10:56,690 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:56,690 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:56,690 INFO sqlalchemy.engine.Engine [cached since 40.03s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:10:56.631237', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Retrieved policy texts: [\'privacy_policy\', \'terms_and_condition\', \'contact_us\', \'about_us\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:10:56.631228", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:10:56,690 - sqlalchemy.engine.Engine - INFO - [cached since 40.03s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:10:56.631237', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Retrieved policy texts: [\'privacy_policy\', \'terms_and_condition\', \'contact_us\', \'about_us\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:10:56.631228", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:10:56,751 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:10:56,751 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 19:10:56,885 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-03 19:10:56,885 - sqlalchemy.engine.Engine - INFO - ROLLBACK
[2025-08-03 19:10:57][EntityExtractor][180][************************************] INFO: Found policy texts for 4 policy types
2025-08-03 19:10:57,110 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:10:57,110 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:10:57,110 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:57,110 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:57,111 INFO sqlalchemy.engine.Engine [cached since 40.45s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:10:57.056766', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found policy texts for 4 policy types", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:10:57.056758", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:10:57,111 - sqlalchemy.engine.Engine - INFO - [cached since 40.45s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:10:57.056766', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found policy texts for 4 policy types", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:10:57.056758", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:10:57,172 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:10:57,172 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:10:57][EntityExtractor][180][************************************] INFO: Processing contact_info with fields: ['support_email']
2025-08-03 19:10:57,501 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:10:57,501 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:10:57,501 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:57,501 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:57,501 INFO sqlalchemy.engine.Engine [cached since 40.84s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:10:57.450376', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Processing contact_info with fields: [\'support_email\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:10:57.450364", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:10:57,501 - sqlalchemy.engine.Engine - INFO - [cached since 40.84s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:10:57.450376', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Processing contact_info with fields: [\'support_email\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:10:57.450364", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:10:57,568 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:10:57,568 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 19:10:59,201 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 19:10:59,271 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:10:59,271 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:10:59,271 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:59,271 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:59,271 INFO sqlalchemy.engine.Engine [cached since 42.61s ago] {'analysis_id': 0, 'timestamp': '2025-08-03 19:10:57', 'type': 'openai', 'messages': '[\n    {\n        "role": "user",\n        "content": "\\nYou are an expert at extracting business information from policy documents and website cont ... (10420 characters truncated) ... \\n{\\n    \\"support_email\\": \\"extracted support/customer email or null\\"\\n}\\n\\nReturn only the JSON response, no additional text."\n    }\n]', 'response': '"{\\n    \\"support_email\\": \\"<EMAIL>\\"\\n}"', 'org_id': 'default'}
2025-08-03 19:10:59,271 - sqlalchemy.engine.Engine - INFO - [cached since 42.61s ago] {'analysis_id': 0, 'timestamp': '2025-08-03 19:10:57', 'type': 'openai', 'messages': '[\n    {\n        "role": "user",\n        "content": "\\nYou are an expert at extracting business information from policy documents and website cont ... (10420 characters truncated) ... \\n{\\n    \\"support_email\\": \\"extracted support/customer email or null\\"\\n}\\n\\nReturn only the JSON response, no additional text."\n    }\n]', 'response': '"{\\n    \\"support_email\\": \\"<EMAIL>\\"\\n}"', 'org_id': 'default'}
2025-08-03 19:10:59,354 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:10:59,354 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:10:59][api_response_logger][unknown] INFO: OpenAI API Call - Name: entity_extraction_contact_info, Request ID: None
{
  "response_preview": "{\n    \"support_email\": \"<EMAIL>\"\n}"
}
[2025-08-03 19:10:59][api_response_logger][unknown] INFO: OpenAI API Call - Name: entity_extraction_contact_info, Request ID: ************************************
{
  "response_preview": "{\n    \"support_email\": \"<EMAIL>\"\n}"
}
[2025-08-03 19:10:59][EntityExtractor][180][************************************] INFO: OpenAI contact_info call extracted 1 fields: ['support_email']
2025-08-03 19:10:59,534 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:10:59,534 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:10:59,534 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:59,534 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:59,534 INFO sqlalchemy.engine.Engine [cached since 42.87s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:10:59.461210', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "OpenAI contact_info call extracted 1 fields: [\'support_email\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:10:59.461200", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:10:59,534 - sqlalchemy.engine.Engine - INFO - [cached since 42.87s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:10:59.461210', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "OpenAI contact_info call extracted 1 fields: [\'support_email\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:10:59.461200", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:10:59,604 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:10:59,604 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:10:59][EntityExtractor][180][************************************] INFO: Processing legal_info with fields: ['jurisdiction_place', 'jurisdiction_details']
2025-08-03 19:10:59,784 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:10:59,784 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:10:59,785 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:59,785 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:10:59,785 INFO sqlalchemy.engine.Engine [cached since 43.12s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:10:59.718721', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Processing legal_info with fields: [\'jurisdiction_place\', \'jurisdiction_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:10:59.718711", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:10:59,785 - sqlalchemy.engine.Engine - INFO - [cached since 43.12s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:10:59.718721', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Processing legal_info with fields: [\'jurisdiction_place\', \'jurisdiction_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:10:59.718711", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:10:59,831 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:10:59,831 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 19:11:01,351 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 19:11:01,413 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:11:01,413 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:11:01,414 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:11:01,414 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:11:01,414 INFO sqlalchemy.engine.Engine [cached since 44.75s ago] {'analysis_id': 0, 'timestamp': '2025-08-03 19:10:59', 'type': 'openai', 'messages': '[\n    {\n        "role": "user",\n        "content": "\\nYou are an expert at extracting business information from policy documents and website cont ... (12065 characters truncated) ... \n    \\"jurisdiction_details\\": \\"extracted jurisdiction details or null\\"\\n}\\n\\nReturn only the JSON response, no additional text."\n    }\n]', 'response': '"{\\n    \\"jurisdiction_place\\": \\"India\\",\\n    \\"jurisdiction_details\\": \\"These Terms and Conditions are governed by the laws of India.\\"\\n}"', 'org_id': 'default'}
2025-08-03 19:11:01,414 - sqlalchemy.engine.Engine - INFO - [cached since 44.75s ago] {'analysis_id': 0, 'timestamp': '2025-08-03 19:10:59', 'type': 'openai', 'messages': '[\n    {\n        "role": "user",\n        "content": "\\nYou are an expert at extracting business information from policy documents and website cont ... (12065 characters truncated) ... \n    \\"jurisdiction_details\\": \\"extracted jurisdiction details or null\\"\\n}\\n\\nReturn only the JSON response, no additional text."\n    }\n]', 'response': '"{\\n    \\"jurisdiction_place\\": \\"India\\",\\n    \\"jurisdiction_details\\": \\"These Terms and Conditions are governed by the laws of India.\\"\\n}"', 'org_id': 'default'}
2025-08-03 19:11:01,494 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:11:01,494 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:11:01][api_response_logger][unknown] INFO: OpenAI API Call - Name: entity_extraction_legal_info, Request ID: None
{
  "response_preview": "{\n    \"jurisdiction_place\": \"India\",\n    \"jurisdiction_details\": \"These Terms and Conditions are governed by the laws of India.\"\n}"
}
[2025-08-03 19:11:01][api_response_logger][unknown] INFO: OpenAI API Call - Name: entity_extraction_legal_info, Request ID: ************************************
{
  "response_preview": "{\n    \"jurisdiction_place\": \"India\",\n    \"jurisdiction_details\": \"These Terms and Conditions are governed by the laws of India.\"\n}"
}
[2025-08-03 19:11:01][EntityExtractor][180][************************************] INFO: OpenAI legal_info call extracted 2 fields: ['jurisdiction_place', 'jurisdiction_details']
2025-08-03 19:11:01,659 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:11:01,659 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:11:01,660 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:11:01,660 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:11:01,660 INFO sqlalchemy.engine.Engine [cached since 45s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:11:01.602766', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "OpenAI legal_info call extracted 2 fields: [\'jurisdiction_place\', \'jurisdiction_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:11:01.602761", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:11:01,660 - sqlalchemy.engine.Engine - INFO - [cached since 45s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:11:01.602766', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "OpenAI legal_info call extracted 2 fields: [\'jurisdiction_place\', \'jurisdiction_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:11:01.602761", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:11:01,721 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:11:01,721 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:11:01][EntityExtractor][180][************************************] INFO: Processing shipping_info with fields: ['shipping_countries', 'shipping_policy_details']
2025-08-03 19:11:01,901 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:11:01,901 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:11:01,901 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:11:01,901 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:11:01,901 INFO sqlalchemy.engine.Engine [cached since 45.24s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:11:01.850634', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Processing shipping_info with fields: [\'shipping_countries\', \'shipping_policy_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:11:01.850623", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:11:01,901 - sqlalchemy.engine.Engine - INFO - [cached since 45.24s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:11:01.850634', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Processing shipping_info with fields: [\'shipping_countries\', \'shipping_policy_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:11:01.850623", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:11:01,969 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:11:01,969 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 19:11:03,191 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 19:11:03,250 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:11:03,250 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:11:03,251 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:11:03,251 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:11:03,251 INFO sqlalchemy.engine.Engine [cached since 46.59s ago] {'analysis_id': 0, 'timestamp': '2025-08-03 19:11:02', 'type': 'openai', 'messages': '[\n    {\n        "role": "user",\n        "content": "\\nYou are an expert at extracting business information from policy documents and website cont ... (2726 characters truncated) ... \\"shipping_policy_details\\": \\"extracted shipping policy details or null\\"\\n}\\n\\nReturn only the JSON response, no additional text."\n    }\n]', 'response': '"{\\n    \\"shipping_countries\\": \\"null\\",\\n    \\"shipping_policy_details\\": \\"null\\"\\n}"', 'org_id': 'default'}
2025-08-03 19:11:03,251 - sqlalchemy.engine.Engine - INFO - [cached since 46.59s ago] {'analysis_id': 0, 'timestamp': '2025-08-03 19:11:02', 'type': 'openai', 'messages': '[\n    {\n        "role": "user",\n        "content": "\\nYou are an expert at extracting business information from policy documents and website cont ... (2726 characters truncated) ... \\"shipping_policy_details\\": \\"extracted shipping policy details or null\\"\\n}\\n\\nReturn only the JSON response, no additional text."\n    }\n]', 'response': '"{\\n    \\"shipping_countries\\": \\"null\\",\\n    \\"shipping_policy_details\\": \\"null\\"\\n}"', 'org_id': 'default'}
2025-08-03 19:11:03,328 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:11:03,328 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:11:03][api_response_logger][unknown] INFO: OpenAI API Call - Name: entity_extraction_shipping_info, Request ID: None
{
  "response_preview": "{\n    \"shipping_countries\": \"null\",\n    \"shipping_policy_details\": \"null\"\n}"
}
[2025-08-03 19:11:03][api_response_logger][unknown] INFO: OpenAI API Call - Name: entity_extraction_shipping_info, Request ID: ************************************
{
  "response_preview": "{\n    \"shipping_countries\": \"null\",\n    \"shipping_policy_details\": \"null\"\n}"
}
[2025-08-03 19:11:03][EntityExtractor][180][************************************] INFO: OpenAI shipping_info call extracted 0 fields: []
2025-08-03 19:11:03,489 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:11:03,489 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:11:03,489 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:11:03,489 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:11:03,490 INFO sqlalchemy.engine.Engine [cached since 46.83s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:11:03.431023', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "OpenAI shipping_info call extracted 0 fields: []", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:11:03.431017", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:11:03,490 - sqlalchemy.engine.Engine - INFO - [cached since 46.83s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:11:03.431023', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "OpenAI shipping_info call extracted 0 fields: []", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:11:03.431017", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:11:03,552 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:11:03,552 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:11:03][EntityExtractor][180][************************************] INFO: Making final comprehensive call for remaining fields: ['shipping_countries', 'shipping_policy_details']
2025-08-03 19:11:03,724 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:11:03,724 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:11:03,724 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:11:03,724 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:11:03,725 INFO sqlalchemy.engine.Engine [cached since 47.06s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:11:03.671293', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Making final comprehensive call for remaining fields: [\'shipping_countries\', \'shipping_policy_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:11:03.671282", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:11:03,725 - sqlalchemy.engine.Engine - INFO - [cached since 47.06s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:11:03.671293', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Making final comprehensive call for remaining fields: [\'shipping_countries\', \'shipping_policy_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:11:03.671282", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:11:03,782 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:11:03,782 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 19:11:05,134 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 19:11:05,189 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:11:05,189 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:11:05,190 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:11:05,190 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:11:05,190 INFO sqlalchemy.engine.Engine [cached since 48.53s ago] {'analysis_id': 0, 'timestamp': '2025-08-03 19:11:03', 'type': 'openai', 'messages': '[\n    {\n        "role": "user",\n        "content": "\\nYou are an expert at extracting business information from policy documents and website cont ... (14443 characters truncated) ... \\"shipping_policy_details\\": \\"extracted shipping policy details or null\\"\\n}\\n\\nReturn only the JSON response, no additional text."\n    }\n]', 'response': '"{\\n    \\"shipping_countries\\": \\"null\\",\\n    \\"shipping_policy_details\\": \\"null\\"\\n}"', 'org_id': 'default'}
2025-08-03 19:11:05,190 - sqlalchemy.engine.Engine - INFO - [cached since 48.53s ago] {'analysis_id': 0, 'timestamp': '2025-08-03 19:11:03', 'type': 'openai', 'messages': '[\n    {\n        "role": "user",\n        "content": "\\nYou are an expert at extracting business information from policy documents and website cont ... (14443 characters truncated) ... \\"shipping_policy_details\\": \\"extracted shipping policy details or null\\"\\n}\\n\\nReturn only the JSON response, no additional text."\n    }\n]', 'response': '"{\\n    \\"shipping_countries\\": \\"null\\",\\n    \\"shipping_policy_details\\": \\"null\\"\\n}"', 'org_id': 'default'}
2025-08-03 19:11:05,328 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:11:05,328 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:11:05][api_response_logger][unknown] INFO: OpenAI API Call - Name: entity_extraction_comprehensive, Request ID: None
{
  "response_preview": "{\n    \"shipping_countries\": \"null\",\n    \"shipping_policy_details\": \"null\"\n}"
}
[2025-08-03 19:11:05][api_response_logger][unknown] INFO: OpenAI API Call - Name: entity_extraction_comprehensive, Request ID: ************************************
{
  "response_preview": "{\n    \"shipping_countries\": \"null\",\n    \"shipping_policy_details\": \"null\"\n}"
}
[2025-08-03 19:11:05][EntityExtractor][180][************************************] INFO: OpenAI comprehensive call extracted 0 fields: []
2025-08-03 19:11:05,509 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:11:05,509 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:11:05,509 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:11:05,509 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:11:05,509 INFO sqlalchemy.engine.Engine [cached since 48.85s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:11:05.444305', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "OpenAI comprehensive call extracted 0 fields: []", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:11:05.444299", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:11:05,509 - sqlalchemy.engine.Engine - INFO - [cached since 48.85s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:11:05.444305', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "OpenAI comprehensive call extracted 0 fields: []", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:11:05.444299", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:11:05,580 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:11:05,580 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:11:05][EntityExtractor][180][************************************] INFO: Total OpenAI backup extracted 3 fields: ['support_email', 'jurisdiction_place', 'jurisdiction_details']
2025-08-03 19:11:05,736 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:11:05,736 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:11:05,736 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:11:05,736 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:11:05,737 INFO sqlalchemy.engine.Engine [cached since 49.07s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:11:05.681575', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Total OpenAI backup extracted 3 fields: [\'support_email\', \'jurisdiction_place\', \'jurisdiction_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:11:05.681561", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:11:05,737 - sqlalchemy.engine.Engine - INFO - [cached since 49.07s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:11:05.681575', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Total OpenAI backup extracted 3 fields: [\'support_email\', \'jurisdiction_place\', \'jurisdiction_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:11:05.681561", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:11:05,802 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:11:05,802 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:11:05][EntityExtractor][180][************************************] INFO: OpenAI backup extracted: ['support_email', 'jurisdiction_place', 'jurisdiction_details']
2025-08-03 19:11:06,032 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:11:06,032 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:11:06,032 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:11:06,032 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:11:06,032 INFO sqlalchemy.engine.Engine [cached since 49.37s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:11:05.986346', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "OpenAI backup extracted: [\'support_email\', \'jurisdiction_place\', \'jurisdiction_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:11:05.986337", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:11:06,032 - sqlalchemy.engine.Engine - INFO - [cached since 49.37s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:11:05.986346', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "OpenAI backup extracted: [\'support_email\', \'jurisdiction_place\', \'jurisdiction_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:11:05.986337", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:11:06,128 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:11:06,128 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:11:06][EntityExtractor][180][************************************] INFO: Merging 2 AI extraction results
2025-08-03 19:11:06,281 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:11:06,281 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:11:06,281 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:11:06,281 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:11:06,282 INFO sqlalchemy.engine.Engine [cached since 49.62s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:11:06.231021', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Merging 2 AI extraction results", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:11:06.231013", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:11:06,282 - sqlalchemy.engine.Engine - INFO - [cached since 49.62s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:11:06.231021', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Merging 2 AI extraction results", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:11:06.231013", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:11:06,343 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:11:06,343 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:11:06][EntityExtractor][180][************************************] INFO: Merged result contains 8 fields
2025-08-03 19:11:06,508 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:11:06,508 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:11:06,509 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:11:06,509 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:11:06,509 INFO sqlalchemy.engine.Engine [cached since 49.84s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:11:06.461909', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Merged result contains 8 fields", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:11:06.461902", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:11:06,509 - sqlalchemy.engine.Engine - INFO - [cached since 49.84s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:11:06.461909', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Merged result contains 8 fields", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:11:06.461902", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:11:06,554 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:11:06,554 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:11:06][EntityExtractor][180][************************************] INFO: Final merged extraction result keys: ['legal_name', 'business_email', 'business_contact_numbers', 'business_location', 'has_jurisdiction_law', 'support_email', 'jurisdiction_place', 'jurisdiction_details']
2025-08-03 19:11:06,742 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:11:06,742 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:11:06,742 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:11:06,742 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:11:06,742 INFO sqlalchemy.engine.Engine [cached since 50.08s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:11:06.671427', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Final merged extraction result keys: [\'legal_name\', \'business_email\', \'business_contact_numbers\', \'business_loca ... (75 characters truncated) ... , \'jurisdiction_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:11:06.671420", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:11:06,742 - sqlalchemy.engine.Engine - INFO - [cached since 50.08s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:11:06.671427', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Final merged extraction result keys: [\'legal_name\', \'business_email\', \'business_contact_numbers\', \'business_loca ... (75 characters truncated) ... , \'jurisdiction_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:11:06.671420", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:11:06,793 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:11:06,793 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:11:06][EntityExtractor][180][************************************] INFO: Fallback raw text for privacy_policy_text: present
2025-08-03 19:11:07,005 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:11:07,005 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:11:07,006 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:11:07,006 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:11:07,006 INFO sqlalchemy.engine.Engine [cached since 50.34s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:11:06.925622', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Fallback raw text for privacy_policy_text: present", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:11:06.925610", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:11:07,006 - sqlalchemy.engine.Engine - INFO - [cached since 50.34s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:11:06.925622', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Fallback raw text for privacy_policy_text: present", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:11:06.925610", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:11:07,059 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:11:07,059 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:11:07][EntityExtractor][180][************************************] INFO: After fallback, merged_result[privacy_policy_text] length: 2340
2025-08-03 19:11:07,231 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:11:07,231 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:11:07,232 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:11:07,232 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:11:07,232 INFO sqlalchemy.engine.Engine [cached since 50.57s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:11:07.180828', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "After fallback, merged_result[privacy_policy_text] length: 2340", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:11:07.180820", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:11:07,232 - sqlalchemy.engine.Engine - INFO - [cached since 50.57s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:11:07.180828', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "After fallback, merged_result[privacy_policy_text] length: 2340", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:11:07.180820", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:11:07,297 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:11:07,297 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:11:07][EntityExtractor][180][************************************] INFO: Fallback raw text for terms_conditions_text: present
2025-08-03 19:11:07,485 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:11:07,485 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:11:07,485 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:11:07,485 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:11:07,485 INFO sqlalchemy.engine.Engine [cached since 50.82s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:11:07.421950', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Fallback raw text for terms_conditions_text: present", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:11:07.421938", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:11:07,485 - sqlalchemy.engine.Engine - INFO - [cached since 50.82s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:11:07.421950', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Fallback raw text for terms_conditions_text: present", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:11:07.421938", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:11:07,541 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:11:07,541 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:11:07][EntityExtractor][180][************************************] INFO: After fallback, merged_result[terms_conditions_text] length: 1479
2025-08-03 19:11:07,691 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:11:07,691 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:11:07,691 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:11:07,691 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:11:07,692 INFO sqlalchemy.engine.Engine [cached since 51.03s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:11:07.642036', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "After fallback, merged_result[terms_conditions_text] length: 1479", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:11:07.642023", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:11:07,692 - sqlalchemy.engine.Engine - INFO - [cached since 51.03s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:11:07.642036', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "After fallback, merged_result[terms_conditions_text] length: 1479", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:11:07.642023", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:11:07,751 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:11:07,751 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 19:11:07,911 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:11:07,911 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:11:07,912 INFO sqlalchemy.engine.Engine SELECT entity_extraction_analysis.id AS entity_extraction_analysis_id, entity_extraction_analysis.scrape_request_ref_id AS entity_extraction_analysis_scrape_request_ref_id, entity_extraction_analysis.website_url AS entity_extraction_analysis_website_url, entity_extraction_analysis.processing_status AS entity_extraction_analysis_processing_status, entity_extraction_analysis.legal_name AS entity_extraction_analysis_legal_name, entity_extraction_analysis.business_email AS entity_extraction_analysis_business_email, entity_extraction_analysis.support_email AS entity_extraction_analysis_support_email, entity_extraction_analysis.business_contact_numbers AS entity_extraction_analysis_business_contact_numbers, entity_extraction_analysis.business_location AS entity_extraction_analysis_business_location, entity_extraction_analysis.has_jurisdiction_law AS entity_extraction_analysis_has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details AS entity_extraction_analysis_jurisdiction_details, entity_extraction_analysis.accepts_international_orders AS entity_extraction_analysis_accepts_international_orders, entity_extraction_analysis.shipping_policy_details AS entity_extraction_analysis_shipping_policy_details, entity_extraction_analysis.jurisdiction_place AS entity_extraction_analysis_jurisdiction_place, entity_extraction_analysis.shipping_countries AS entity_extraction_analysis_shipping_countries, entity_extraction_analysis.privacy_policy_text AS entity_extraction_analysis_privacy_policy_text, entity_extraction_analysis.terms_conditions_text AS entity_extraction_analysis_terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini AS entity_extraction_analysis_urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini AS entity_extraction_analysis_urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method AS entity_extraction_analysis_extraction_method, entity_extraction_analysis.total_urls_processed AS entity_extraction_analysis_total_urls_processed, entity_extraction_analysis.all_urls_found AS entity_extraction_analysis_all_urls_found, entity_extraction_analysis.reachable_urls AS entity_extraction_analysis_reachable_urls, entity_extraction_analysis.unreachable_urls AS entity_extraction_analysis_unreachable_urls, entity_extraction_analysis.policy_urls_matched AS entity_extraction_analysis_policy_urls_matched, entity_extraction_analysis.created_at AS entity_extraction_analysis_created_at, entity_extraction_analysis.started_at AS entity_extraction_analysis_started_at, entity_extraction_analysis.completed_at AS entity_extraction_analysis_completed_at, entity_extraction_analysis.error_message AS entity_extraction_analysis_error_message, entity_extraction_analysis.org_id AS entity_extraction_analysis_org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.id = %(pk_1)s
2025-08-03 19:11:07,912 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_analysis.id AS entity_extraction_analysis_id, entity_extraction_analysis.scrape_request_ref_id AS entity_extraction_analysis_scrape_request_ref_id, entity_extraction_analysis.website_url AS entity_extraction_analysis_website_url, entity_extraction_analysis.processing_status AS entity_extraction_analysis_processing_status, entity_extraction_analysis.legal_name AS entity_extraction_analysis_legal_name, entity_extraction_analysis.business_email AS entity_extraction_analysis_business_email, entity_extraction_analysis.support_email AS entity_extraction_analysis_support_email, entity_extraction_analysis.business_contact_numbers AS entity_extraction_analysis_business_contact_numbers, entity_extraction_analysis.business_location AS entity_extraction_analysis_business_location, entity_extraction_analysis.has_jurisdiction_law AS entity_extraction_analysis_has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details AS entity_extraction_analysis_jurisdiction_details, entity_extraction_analysis.accepts_international_orders AS entity_extraction_analysis_accepts_international_orders, entity_extraction_analysis.shipping_policy_details AS entity_extraction_analysis_shipping_policy_details, entity_extraction_analysis.jurisdiction_place AS entity_extraction_analysis_jurisdiction_place, entity_extraction_analysis.shipping_countries AS entity_extraction_analysis_shipping_countries, entity_extraction_analysis.privacy_policy_text AS entity_extraction_analysis_privacy_policy_text, entity_extraction_analysis.terms_conditions_text AS entity_extraction_analysis_terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini AS entity_extraction_analysis_urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini AS entity_extraction_analysis_urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method AS entity_extraction_analysis_extraction_method, entity_extraction_analysis.total_urls_processed AS entity_extraction_analysis_total_urls_processed, entity_extraction_analysis.all_urls_found AS entity_extraction_analysis_all_urls_found, entity_extraction_analysis.reachable_urls AS entity_extraction_analysis_reachable_urls, entity_extraction_analysis.unreachable_urls AS entity_extraction_analysis_unreachable_urls, entity_extraction_analysis.policy_urls_matched AS entity_extraction_analysis_policy_urls_matched, entity_extraction_analysis.created_at AS entity_extraction_analysis_created_at, entity_extraction_analysis.started_at AS entity_extraction_analysis_started_at, entity_extraction_analysis.completed_at AS entity_extraction_analysis_completed_at, entity_extraction_analysis.error_message AS entity_extraction_analysis_error_message, entity_extraction_analysis.org_id AS entity_extraction_analysis_org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.id = %(pk_1)s
2025-08-03 19:11:07,912 INFO sqlalchemy.engine.Engine [cached since 49.12s ago] {'pk_1': 180}
2025-08-03 19:11:07,912 - sqlalchemy.engine.Engine - INFO - [cached since 49.12s ago] {'pk_1': 180}
2025-08-03 19:11:07,984 INFO sqlalchemy.engine.Engine UPDATE entity_extraction_analysis SET processing_status=%(processing_status)s, business_location=%(business_location)s, jurisdiction_details=%(jurisdiction_details)s, completed_at=%(completed_at)s WHERE entity_extraction_analysis.id = %(entity_extraction_analysis_id)s
2025-08-03 19:11:07,984 - sqlalchemy.engine.Engine - INFO - UPDATE entity_extraction_analysis SET processing_status=%(processing_status)s, business_location=%(business_location)s, jurisdiction_details=%(jurisdiction_details)s, completed_at=%(completed_at)s WHERE entity_extraction_analysis.id = %(entity_extraction_analysis_id)s
2025-08-03 19:11:07,985 INFO sqlalchemy.engine.Engine [generated in 0.00055s] {'processing_status': 'COMPLETED', 'business_location': '2nd Floor, Campus 4A, RMZ Millenia Business Park, 143, Dr MGR Road, Perungudi, Chennai - 600 096, India, Commerzone, Block-2, No.2, 200 Feet Radial Road, Pallikaranai, Chennai – 600100, India', 'jurisdiction_details': 'These Terms and Conditions are governed by the laws of India.', 'completed_at': '2025-08-03T19:11:07.983037', 'entity_extraction_analysis_id': 180}
2025-08-03 19:11:07,985 - sqlalchemy.engine.Engine - INFO - [generated in 0.00055s] {'processing_status': 'COMPLETED', 'business_location': '2nd Floor, Campus 4A, RMZ Millenia Business Park, 143, Dr MGR Road, Perungudi, Chennai - 600 096, India, Commerzone, Block-2, No.2, 200 Feet Radial Road, Pallikaranai, Chennai – 600100, India', 'jurisdiction_details': 'These Terms and Conditions are governed by the laws of India.', 'completed_at': '2025-08-03T19:11:07.983037', 'entity_extraction_analysis_id': 180}
2025-08-03 19:11:08,044 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:11:08,044 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:11:08][EntityExtractor][180][************************************] INFO: Updated analysis 180 with merged results and completed_at timestamp
2025-08-03 19:11:08,207 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:11:08,207 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:11:08,207 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:11:08,207 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:11:08,207 INFO sqlalchemy.engine.Engine [cached since 51.54s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:11:08.154714', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Updated analysis 180 with merged results and completed_at timestamp", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:11:08.154700", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:11:08,207 - sqlalchemy.engine.Engine - INFO - [cached since 51.54s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:11:08.154714', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Updated analysis 180 with merged results and completed_at timestamp", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:11:08.154700", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:11:08,276 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:11:08,276 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:11:08][EntityExtractor][180][************************************] INFO: Creating response with merged_result keys: ['legal_name', 'business_email', 'business_contact_numbers', 'business_location', 'has_jurisdiction_law', 'support_email', 'jurisdiction_place', 'jurisdiction_details', 'privacy_policy_text', 'terms_conditions_text']
2025-08-03 19:11:08,458 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:11:08,458 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:11:08,458 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:11:08,458 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:11:08,458 INFO sqlalchemy.engine.Engine [cached since 51.79s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:11:08.396785', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Creating response with merged_result keys: [\'legal_name\', \'business_email\', \'business_contact_numbers\', \'busines ... (133 characters truncated) ...  \'terms_conditions_text\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:11:08.396777", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:11:08,458 - sqlalchemy.engine.Engine - INFO - [cached since 51.79s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:11:08.396785', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Creating response with merged_result keys: [\'legal_name\', \'business_email\', \'business_contact_numbers\', \'busines ... (133 characters truncated) ...  \'terms_conditions_text\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:11:08.396777", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:11:08,517 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:11:08,517 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:11:08][EntityExtractor][180][************************************] INFO: Response jurisdiction_details: These Terms and Conditions are governed by the laws of India.
2025-08-03 19:11:08,681 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:11:08,681 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:11:08,682 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:11:08,682 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:11:08,682 INFO sqlalchemy.engine.Engine [cached since 52.02s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:11:08.630813', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Response jurisdiction_details: These Terms and Conditions are governed by the laws of India.", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:11:08.630804", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:11:08,682 - sqlalchemy.engine.Engine - INFO - [cached since 52.02s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:11:08.630813', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Response jurisdiction_details: These Terms and Conditions are governed by the laws of India.", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:11:08.630804", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:11:08,743 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:11:08,743 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:11:08][EntityExtractor][180][************************************] INFO: Response jurisdiction_place: []
2025-08-03 19:11:08,932 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:11:08,932 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:11:08,932 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:11:08,932 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:11:08,932 INFO sqlalchemy.engine.Engine [cached since 52.27s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:11:08.863077', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Response jurisdiction_place: []", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:11:08.863067", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:11:08,932 - sqlalchemy.engine.Engine - INFO - [cached since 52.27s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:11:08.863077', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Response jurisdiction_place: []", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:11:08.863067", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:11:09,025 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:11:09,025 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:11:09][EntityExtractor][180][************************************] INFO: Response has_jurisdiction_law: yes
2025-08-03 19:11:09,226 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:11:09,226 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:11:09,226 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:11:09,226 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:11:09,227 INFO sqlalchemy.engine.Engine [cached since 52.56s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:11:09.160161', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Response has_jurisdiction_law: yes", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:11:09.160150", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:11:09,227 - sqlalchemy.engine.Engine - INFO - [cached since 52.56s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:11:09.160161', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Response has_jurisdiction_law: yes", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:11:09.160150", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:11:09,301 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:11:09,301 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:11:09][EntityExtractor][background_processor][background] INFO: Entity extraction completed for https://www.shell.in
2025-08-03 19:11:09,459 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:11:09,459 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:11:09,460 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:11:09,460 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:11:09,460 INFO sqlalchemy.engine.Engine [cached since 52.8s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:11:09.402119', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Entity extraction completed for https://www.shell.in", "scrape_request_ref_id": "background", "timestamp": "2025-08-03T19:11:09.402110", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:11:09,460 - sqlalchemy.engine.Engine - INFO - [cached since 52.8s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:11:09.402119', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Entity extraction completed for https://www.shell.in", "scrape_request_ref_id": "background", "timestamp": "2025-08-03T19:11:09.402110", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:11:09,532 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:11:09,532 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:11:09][EntityExtractor][background_processor][background] INFO: Sending extraction results to Java server (sync) for https://www.shell.in
2025-08-03 19:11:09,682 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:11:09,682 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:11:09,683 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:11:09,683 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:11:09,683 INFO sqlalchemy.engine.Engine [cached since 53.02s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:11:09.631938', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Sending extraction results to Java server (sync) for https://www.shell.in", "scrape_request_ref_id": "background", "timestamp": "2025-08-03T19:11:09.631923", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:11:09,683 - sqlalchemy.engine.Engine - INFO - [cached since 53.02s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:11:09.631938', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Sending extraction results to Java server (sync) for https://www.shell.in", "scrape_request_ref_id": "background", "timestamp": "2025-08-03T19:11:09.631923", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:11:09,751 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:11:09,751 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:11:09][EntityExtractor][background_processor][background] INFO: Sending PATCH request to: http://localhost:8080/api/entity-extraction/results
2025-08-03 19:11:09,920 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:11:09,920 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:11:09,921 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:11:09,921 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:11:09,921 INFO sqlalchemy.engine.Engine [cached since 53.26s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:11:09.870845', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Sending PATCH request to: http://localhost:8080/api/entity-extraction/results", "scrape_request_ref_id": "background", "timestamp": "2025-08-03T19:11:09.870836", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:11:09,921 - sqlalchemy.engine.Engine - INFO - [cached since 53.26s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:11:09.870845', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Sending PATCH request to: http://localhost:8080/api/entity-extraction/results", "scrape_request_ref_id": "background", "timestamp": "2025-08-03T19:11:09.870836", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:11:09,982 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:11:09,982 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:11:10][EntityExtractor][background_processor][background] DEBUG: Payload org_id type: <class 'int'>, value: 2
2025-08-03 19:11:10,141 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:11:10,141 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:11:10,142 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:11:10,142 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:11:10,142 INFO sqlalchemy.engine.Engine [cached since 53.48s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:11:10.092879', 'type': 'entity_extractor', 'messages': '{"level": "DEBUG", "message": "Payload org_id type: <class \'int\'>, value: 2", "scrape_request_ref_id": "background", "timestamp": "2025-08-03T19:11:10.092866", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:11:10,142 - sqlalchemy.engine.Engine - INFO - [cached since 53.48s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:11:10.092879', 'type': 'entity_extractor', 'messages': '{"level": "DEBUG", "message": "Payload org_id type: <class \'int\'>, value: 2", "scrape_request_ref_id": "background", "timestamp": "2025-08-03T19:11:10.092866", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:11:10,205 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:11:10,205 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:11:10][EntityExtractor][background_processor][background] ERROR: Request error sending results to Java server: [Errno 111] Connection refused
2025-08-03 19:11:10,374 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:11:10,374 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:11:10,375 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:11:10,375 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:11:10,375 INFO sqlalchemy.engine.Engine [cached since 53.71s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:11:10.320335', 'type': 'entity_extractor', 'messages': '{"level": "ERROR", "message": "Request error sending results to Java server: [Errno 111] Connection refused", "scrape_request_ref_id": "background", "timestamp": "2025-08-03T19:11:10.320323", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:11:10,375 - sqlalchemy.engine.Engine - INFO - [cached since 53.71s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:11:10.320335', 'type': 'entity_extractor', 'messages': '{"level": "ERROR", "message": "Request error sending results to Java server: [Errno 111] Connection refused", "scrape_request_ref_id": "background", "timestamp": "2025-08-03T19:11:10.320323", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:11:10,442 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:11:10,442 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:11:10][EntityExtractor][background_processor][background] ERROR: Failed to send results to Java server: Request error sending results to Java server: [Errno 111] Connection refused
2025-08-03 19:11:10,591 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:11:10,591 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:11:10,592 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:11:10,592 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:11:10,592 INFO sqlalchemy.engine.Engine [cached since 53.93s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:11:10.541780', 'type': 'entity_extractor', 'messages': '{"level": "ERROR", "message": "Failed to send results to Java server: Request error sending results to Java server: [Errno 111] Connection refused", "scrape_request_ref_id": "background", "timestamp": "2025-08-03T19:11:10.541766", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:11:10,592 - sqlalchemy.engine.Engine - INFO - [cached since 53.93s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:11:10.541780', 'type': 'entity_extractor', 'messages': '{"level": "ERROR", "message": "Failed to send results to Java server: Request error sending results to Java server: [Errno 111] Connection refused", "scrape_request_ref_id": "background", "timestamp": "2025-08-03T19:11:10.541766", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:11:10,661 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:11:10,661 - sqlalchemy.engine.Engine - INFO - COMMIT
