nohup: ignoring input
INFO:     Started server process [102007]
INFO:     Waiting for application startup.
2025-08-03 19:21:10,749 - app.main - INFO - Initializing Entity Extraction API
[API_LOGGER] Log directory created/verified: /home/<USER>/Desktop/WebReview_DS_API_24Jun/api_logs
2025-08-03 19:21:11,132 INFO sqlalchemy.engine.Engine SELECT DATABASE()
2025-08-03 19:21:11,132 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-03 19:21:11,132 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 19:21:11,132 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 19:21:11,241 INFO sqlalchemy.engine.Engine SELECT @@sql_mode
2025-08-03 19:21:11,241 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-03 19:21:11,241 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 19:21:11,241 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 19:21:11,309 INFO sqlalchemy.engine.Engine SELECT @@lower_case_table_names
2025-08-03 19:21:11,309 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-03 19:21:11,309 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 19:21:11,309 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 19:21:11,411 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:21:11,411 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:21:11,411 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`website_urls_gemini`
2025-08-03 19:21:11,411 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`website_urls_gemini`
2025-08-03 19:21:11,411 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 19:21:11,411 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 19:21:11,472 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`general_logs_gemini`
2025-08-03 19:21:11,472 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`general_logs_gemini`
2025-08-03 19:21:11,472 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 19:21:11,472 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 19:21:11,530 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`mcc_url_classification_gemini`
2025-08-03 19:21:11,530 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`mcc_url_classification_gemini`
2025-08-03 19:21:11,530 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 19:21:11,530 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 19:21:11,582 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`policy_url_classification_gemini`
2025-08-03 19:21:11,582 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`policy_url_classification_gemini`
2025-08-03 19:21:11,583 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 19:21:11,583 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 19:21:11,652 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`policy_analysis_new_gemini`
2025-08-03 19:21:11,652 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`policy_analysis_new_gemini`
2025-08-03 19:21:11,652 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 19:21:11,652 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 19:21:11,705 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`entity_extraction_analysis`
2025-08-03 19:21:11,705 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`entity_extraction_analysis`
2025-08-03 19:21:11,705 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 19:21:11,705 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 19:21:11,763 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`entity_extraction_url_analysis`
2025-08-03 19:21:11,763 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`entity_extraction_url_analysis`
2025-08-03 19:21:11,763 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 19:21:11,763 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 19:21:11,834 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:21:11,834 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 19:21:11,936 - app.main - INFO - Database initialized successfully
Creating Entity Extractor database tables...
2025-08-03 19:21:11,992 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:21:11,992 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:21:11,992 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`website_urls_gemini`
2025-08-03 19:21:11,992 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`website_urls_gemini`
2025-08-03 19:21:11,992 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 19:21:11,992 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 19:21:12,062 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`general_logs_gemini`
2025-08-03 19:21:12,062 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`general_logs_gemini`
2025-08-03 19:21:12,062 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 19:21:12,062 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 19:21:12,134 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`mcc_url_classification_gemini`
2025-08-03 19:21:12,134 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`mcc_url_classification_gemini`
2025-08-03 19:21:12,134 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 19:21:12,134 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 19:21:12,194 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`policy_url_classification_gemini`
2025-08-03 19:21:12,194 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`policy_url_classification_gemini`
2025-08-03 19:21:12,194 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 19:21:12,194 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 19:21:12,247 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`policy_analysis_new_gemini`
2025-08-03 19:21:12,247 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`policy_analysis_new_gemini`
2025-08-03 19:21:12,248 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 19:21:12,248 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 19:21:12,300 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`entity_extraction_analysis`
2025-08-03 19:21:12,300 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`entity_extraction_analysis`
2025-08-03 19:21:12,300 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 19:21:12,300 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 19:21:12,351 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`entity_extraction_url_analysis`
2025-08-03 19:21:12,351 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`entity_extraction_url_analysis`
2025-08-03 19:21:12,352 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 19:21:12,352 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 19:21:12,424 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:21:12,424 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 19:21:12,579 - app.main - INFO - Entity Extractor tables initialized successfully
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:9000 (Press CTRL+C to quit)
✅ Entity Extractor database tables created successfully!
Tables created:
  - entity_extraction_analysis
  - entity_extraction_url_analysis
INFO:     127.0.0.1:49052 - "POST /entity-extraction/analyze HTTP/1.1" 200 OK
[2025-08-03 19:23:43][EntityExtractor][background_processor][background] INFO: Java server client initialized with base URL: http://localhost:8080
2025-08-03 19:23:43,272 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:23:43,272 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:23:43,274 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:23:43,274 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:23:43,274 INFO sqlalchemy.engine.Engine [generated in 0.00024s] {'analysis_id': 0, 'timestamp': '2025-08-03T19:23:43.203106', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Java server client initialized with base URL: http://localhost:8080", "scrape_request_ref_id": "background", "timestamp": "2025-08-03T19:23:43.203094", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:23:43,274 - sqlalchemy.engine.Engine - INFO - [generated in 0.00024s] {'analysis_id': 0, 'timestamp': '2025-08-03T19:23:43.203106', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Java server client initialized with base URL: http://localhost:8080", "scrape_request_ref_id": "background", "timestamp": "2025-08-03T19:23:43.203094", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:23:43,340 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:23:43,340 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:23:43][EntityExtractor][background_processor][background] INFO: Starting background processing for https://www.shell.in
2025-08-03 19:23:43,522 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:23:43,522 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:23:43,523 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:23:43,523 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:23:43,523 INFO sqlalchemy.engine.Engine [cached since 0.2491s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:23:43.450211', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Starting background processing for https://www.shell.in", "scrape_request_ref_id": "background", "timestamp": "2025-08-03T19:23:43.450200", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:23:43,523 - sqlalchemy.engine.Engine - INFO - [cached since 0.2491s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:23:43.450211', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Starting background processing for https://www.shell.in", "scrape_request_ref_id": "background", "timestamp": "2025-08-03T19:23:43.450200", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:23:43,581 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:23:43,581 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:23:43][EntityExtractor][orchestrator_************************************][************************************] INFO: Starting simplified entity extraction orchestration
{
  "scrape_request_ref_id": "************************************",
  "website_url": "https://www.shell.in",
  "org_id": 2
}
2025-08-03 19:23:43,731 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:23:43,731 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:23:43,731 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:23:43,731 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:23:43,731 INFO sqlalchemy.engine.Engine [cached since 0.4577s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:23:43.681286', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Starting simplified entity extraction orchestration", "scrape_request_ref_id": "************************************",  ... (20 characters truncated) ... 8-03T19:23:43.681277", "data": {"scrape_request_ref_id": "************************************", "website_url": "https://www.shell.in", "org_id": 2}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:23:43,731 - sqlalchemy.engine.Engine - INFO - [cached since 0.4577s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:23:43.681286', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Starting simplified entity extraction orchestration", "scrape_request_ref_id": "************************************",  ... (20 characters truncated) ... 8-03T19:23:43.681277", "data": {"scrape_request_ref_id": "************************************", "website_url": "https://www.shell.in", "org_id": 2}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:23:43,791 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:23:43,791 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 19:23:43,973 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:23:43,973 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:23:43,977 INFO sqlalchemy.engine.Engine SELECT entity_extraction_analysis.id, entity_extraction_analysis.scrape_request_ref_id, entity_extraction_analysis.website_url, entity_extraction_analysis.processing_status, entity_extraction_analysis.legal_name, entity_extraction_analysis.business_email, entity_extraction_analysis.support_email, entity_extraction_analysis.business_contact_numbers, entity_extraction_analysis.business_location, entity_extraction_analysis.has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details, entity_extraction_analysis.accepts_international_orders, entity_extraction_analysis.shipping_policy_details, entity_extraction_analysis.jurisdiction_place, entity_extraction_analysis.shipping_countries, entity_extraction_analysis.privacy_policy_text, entity_extraction_analysis.terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method, entity_extraction_analysis.total_urls_processed, entity_extraction_analysis.all_urls_found, entity_extraction_analysis.reachable_urls, entity_extraction_analysis.unreachable_urls, entity_extraction_analysis.policy_urls_matched, entity_extraction_analysis.created_at, entity_extraction_analysis.started_at, entity_extraction_analysis.completed_at, entity_extraction_analysis.error_message, entity_extraction_analysis.org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.scrape_request_ref_id = %(scrape_request_ref_id_1)s AND entity_extraction_analysis.org_id = %(org_id_1)s ORDER BY entity_extraction_analysis.id DESC
2025-08-03 19:23:43,977 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_analysis.id, entity_extraction_analysis.scrape_request_ref_id, entity_extraction_analysis.website_url, entity_extraction_analysis.processing_status, entity_extraction_analysis.legal_name, entity_extraction_analysis.business_email, entity_extraction_analysis.support_email, entity_extraction_analysis.business_contact_numbers, entity_extraction_analysis.business_location, entity_extraction_analysis.has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details, entity_extraction_analysis.accepts_international_orders, entity_extraction_analysis.shipping_policy_details, entity_extraction_analysis.jurisdiction_place, entity_extraction_analysis.shipping_countries, entity_extraction_analysis.privacy_policy_text, entity_extraction_analysis.terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method, entity_extraction_analysis.total_urls_processed, entity_extraction_analysis.all_urls_found, entity_extraction_analysis.reachable_urls, entity_extraction_analysis.unreachable_urls, entity_extraction_analysis.policy_urls_matched, entity_extraction_analysis.created_at, entity_extraction_analysis.started_at, entity_extraction_analysis.completed_at, entity_extraction_analysis.error_message, entity_extraction_analysis.org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.scrape_request_ref_id = %(scrape_request_ref_id_1)s AND entity_extraction_analysis.org_id = %(org_id_1)s ORDER BY entity_extraction_analysis.id DESC
2025-08-03 19:23:43,977 INFO sqlalchemy.engine.Engine [generated in 0.00021s] {'scrape_request_ref_id_1': '************************************', 'org_id_1': 2}
2025-08-03 19:23:43,977 - sqlalchemy.engine.Engine - INFO - [generated in 0.00021s] {'scrape_request_ref_id_1': '************************************', 'org_id_1': 2}
[2025-08-03 19:23:44][EntityExtractor][orchestrator_************************************][************************************] INFO: Found existing analysis with ID: 180, status: COMPLETED
2025-08-03 19:23:44,401 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:23:44,401 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:23:44,401 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:23:44,401 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:23:44,401 INFO sqlalchemy.engine.Engine [cached since 1.127s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:23:44.060342', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found existing analysis with ID: 180, status: COMPLETED", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:23:44.060334", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:23:44,401 - sqlalchemy.engine.Engine - INFO - [cached since 1.127s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:23:44.060342', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found existing analysis with ID: 180, status: COMPLETED", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:23:44.060334", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:23:44,472 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:23:44,472 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 19:23:44,571 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-03 19:23:44,571 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-03 19:23:44,741 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:23:44,741 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:23:44,741 INFO sqlalchemy.engine.Engine SELECT entity_extraction_analysis.id, entity_extraction_analysis.scrape_request_ref_id, entity_extraction_analysis.website_url, entity_extraction_analysis.processing_status, entity_extraction_analysis.legal_name, entity_extraction_analysis.business_email, entity_extraction_analysis.support_email, entity_extraction_analysis.business_contact_numbers, entity_extraction_analysis.business_location, entity_extraction_analysis.has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details, entity_extraction_analysis.accepts_international_orders, entity_extraction_analysis.shipping_policy_details, entity_extraction_analysis.jurisdiction_place, entity_extraction_analysis.shipping_countries, entity_extraction_analysis.privacy_policy_text, entity_extraction_analysis.terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method, entity_extraction_analysis.total_urls_processed, entity_extraction_analysis.all_urls_found, entity_extraction_analysis.reachable_urls, entity_extraction_analysis.unreachable_urls, entity_extraction_analysis.policy_urls_matched, entity_extraction_analysis.created_at, entity_extraction_analysis.started_at, entity_extraction_analysis.completed_at, entity_extraction_analysis.error_message, entity_extraction_analysis.org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.scrape_request_ref_id = %(scrape_request_ref_id_1)s AND entity_extraction_analysis.org_id = %(org_id_1)s ORDER BY entity_extraction_analysis.id DESC
2025-08-03 19:23:44,741 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_analysis.id, entity_extraction_analysis.scrape_request_ref_id, entity_extraction_analysis.website_url, entity_extraction_analysis.processing_status, entity_extraction_analysis.legal_name, entity_extraction_analysis.business_email, entity_extraction_analysis.support_email, entity_extraction_analysis.business_contact_numbers, entity_extraction_analysis.business_location, entity_extraction_analysis.has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details, entity_extraction_analysis.accepts_international_orders, entity_extraction_analysis.shipping_policy_details, entity_extraction_analysis.jurisdiction_place, entity_extraction_analysis.shipping_countries, entity_extraction_analysis.privacy_policy_text, entity_extraction_analysis.terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method, entity_extraction_analysis.total_urls_processed, entity_extraction_analysis.all_urls_found, entity_extraction_analysis.reachable_urls, entity_extraction_analysis.unreachable_urls, entity_extraction_analysis.policy_urls_matched, entity_extraction_analysis.created_at, entity_extraction_analysis.started_at, entity_extraction_analysis.completed_at, entity_extraction_analysis.error_message, entity_extraction_analysis.org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.scrape_request_ref_id = %(scrape_request_ref_id_1)s AND entity_extraction_analysis.org_id = %(org_id_1)s ORDER BY entity_extraction_analysis.id DESC
2025-08-03 19:23:44,741 INFO sqlalchemy.engine.Engine [cached since 0.7644s ago] {'scrape_request_ref_id_1': '************************************', 'org_id_1': 2}
2025-08-03 19:23:44,741 - sqlalchemy.engine.Engine - INFO - [cached since 0.7644s ago] {'scrape_request_ref_id_1': '************************************', 'org_id_1': 2}
[2025-08-03 19:23:44][EntityExtractor][orchestrator_************************************][************************************] WARNING: Found existing analysis during create - returning existing ID: 180
2025-08-03 19:23:44,877 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:23:44,877 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:23:44,877 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:23:44,877 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:23:44,877 INFO sqlalchemy.engine.Engine [cached since 1.603s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:23:44.809356', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "Found existing analysis during create - returning existing ID: 180", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:23:44.809348", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:23:44,877 - sqlalchemy.engine.Engine - INFO - [cached since 1.603s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:23:44.809356', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "Found existing analysis during create - returning existing ID: 180", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:23:44.809348", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:23:44,948 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:23:44,948 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 19:23:45,061 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-03 19:23:45,061 - sqlalchemy.engine.Engine - INFO - ROLLBACK
[2025-08-03 19:23:45][EntityExtractor][180][************************************] INFO: Updated logger with analysis ID: 180
2025-08-03 19:23:45,210 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:23:45,210 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:23:45,211 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:23:45,211 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:23:45,211 INFO sqlalchemy.engine.Engine [cached since 1.937s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:23:45.161130', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Updated logger with analysis ID: 180", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:23:45.161120", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:23:45,211 - sqlalchemy.engine.Engine - INFO - [cached since 1.937s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:23:45.161130', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Updated logger with analysis ID: 180", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:23:45.161120", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:23:45,270 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:23:45,270 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 19:23:45,438 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:23:45,438 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:23:45,440 INFO sqlalchemy.engine.Engine SELECT entity_extraction_analysis.id AS entity_extraction_analysis_id, entity_extraction_analysis.scrape_request_ref_id AS entity_extraction_analysis_scrape_request_ref_id, entity_extraction_analysis.website_url AS entity_extraction_analysis_website_url, entity_extraction_analysis.processing_status AS entity_extraction_analysis_processing_status, entity_extraction_analysis.legal_name AS entity_extraction_analysis_legal_name, entity_extraction_analysis.business_email AS entity_extraction_analysis_business_email, entity_extraction_analysis.support_email AS entity_extraction_analysis_support_email, entity_extraction_analysis.business_contact_numbers AS entity_extraction_analysis_business_contact_numbers, entity_extraction_analysis.business_location AS entity_extraction_analysis_business_location, entity_extraction_analysis.has_jurisdiction_law AS entity_extraction_analysis_has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details AS entity_extraction_analysis_jurisdiction_details, entity_extraction_analysis.accepts_international_orders AS entity_extraction_analysis_accepts_international_orders, entity_extraction_analysis.shipping_policy_details AS entity_extraction_analysis_shipping_policy_details, entity_extraction_analysis.jurisdiction_place AS entity_extraction_analysis_jurisdiction_place, entity_extraction_analysis.shipping_countries AS entity_extraction_analysis_shipping_countries, entity_extraction_analysis.privacy_policy_text AS entity_extraction_analysis_privacy_policy_text, entity_extraction_analysis.terms_conditions_text AS entity_extraction_analysis_terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini AS entity_extraction_analysis_urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini AS entity_extraction_analysis_urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method AS entity_extraction_analysis_extraction_method, entity_extraction_analysis.total_urls_processed AS entity_extraction_analysis_total_urls_processed, entity_extraction_analysis.all_urls_found AS entity_extraction_analysis_all_urls_found, entity_extraction_analysis.reachable_urls AS entity_extraction_analysis_reachable_urls, entity_extraction_analysis.unreachable_urls AS entity_extraction_analysis_unreachable_urls, entity_extraction_analysis.policy_urls_matched AS entity_extraction_analysis_policy_urls_matched, entity_extraction_analysis.created_at AS entity_extraction_analysis_created_at, entity_extraction_analysis.started_at AS entity_extraction_analysis_started_at, entity_extraction_analysis.completed_at AS entity_extraction_analysis_completed_at, entity_extraction_analysis.error_message AS entity_extraction_analysis_error_message, entity_extraction_analysis.org_id AS entity_extraction_analysis_org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.id = %(pk_1)s
2025-08-03 19:23:45,440 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_analysis.id AS entity_extraction_analysis_id, entity_extraction_analysis.scrape_request_ref_id AS entity_extraction_analysis_scrape_request_ref_id, entity_extraction_analysis.website_url AS entity_extraction_analysis_website_url, entity_extraction_analysis.processing_status AS entity_extraction_analysis_processing_status, entity_extraction_analysis.legal_name AS entity_extraction_analysis_legal_name, entity_extraction_analysis.business_email AS entity_extraction_analysis_business_email, entity_extraction_analysis.support_email AS entity_extraction_analysis_support_email, entity_extraction_analysis.business_contact_numbers AS entity_extraction_analysis_business_contact_numbers, entity_extraction_analysis.business_location AS entity_extraction_analysis_business_location, entity_extraction_analysis.has_jurisdiction_law AS entity_extraction_analysis_has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details AS entity_extraction_analysis_jurisdiction_details, entity_extraction_analysis.accepts_international_orders AS entity_extraction_analysis_accepts_international_orders, entity_extraction_analysis.shipping_policy_details AS entity_extraction_analysis_shipping_policy_details, entity_extraction_analysis.jurisdiction_place AS entity_extraction_analysis_jurisdiction_place, entity_extraction_analysis.shipping_countries AS entity_extraction_analysis_shipping_countries, entity_extraction_analysis.privacy_policy_text AS entity_extraction_analysis_privacy_policy_text, entity_extraction_analysis.terms_conditions_text AS entity_extraction_analysis_terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini AS entity_extraction_analysis_urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini AS entity_extraction_analysis_urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method AS entity_extraction_analysis_extraction_method, entity_extraction_analysis.total_urls_processed AS entity_extraction_analysis_total_urls_processed, entity_extraction_analysis.all_urls_found AS entity_extraction_analysis_all_urls_found, entity_extraction_analysis.reachable_urls AS entity_extraction_analysis_reachable_urls, entity_extraction_analysis.unreachable_urls AS entity_extraction_analysis_unreachable_urls, entity_extraction_analysis.policy_urls_matched AS entity_extraction_analysis_policy_urls_matched, entity_extraction_analysis.created_at AS entity_extraction_analysis_created_at, entity_extraction_analysis.started_at AS entity_extraction_analysis_started_at, entity_extraction_analysis.completed_at AS entity_extraction_analysis_completed_at, entity_extraction_analysis.error_message AS entity_extraction_analysis_error_message, entity_extraction_analysis.org_id AS entity_extraction_analysis_org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.id = %(pk_1)s
2025-08-03 19:23:45,440 INFO sqlalchemy.engine.Engine [generated in 0.00019s] {'pk_1': 180}
2025-08-03 19:23:45,440 - sqlalchemy.engine.Engine - INFO - [generated in 0.00019s] {'pk_1': 180}
2025-08-03 19:23:45,519 INFO sqlalchemy.engine.Engine UPDATE entity_extraction_analysis SET processing_status=%(processing_status)s, started_at=%(started_at)s WHERE entity_extraction_analysis.id = %(entity_extraction_analysis_id)s
2025-08-03 19:23:45,519 - sqlalchemy.engine.Engine - INFO - UPDATE entity_extraction_analysis SET processing_status=%(processing_status)s, started_at=%(started_at)s WHERE entity_extraction_analysis.id = %(entity_extraction_analysis_id)s
2025-08-03 19:23:45,519 INFO sqlalchemy.engine.Engine [generated in 0.00019s] {'processing_status': 'IN_PROGRESS', 'started_at': '2025-08-03T19:23:45.380856', 'entity_extraction_analysis_id': 180}
2025-08-03 19:23:45,519 - sqlalchemy.engine.Engine - INFO - [generated in 0.00019s] {'processing_status': 'IN_PROGRESS', 'started_at': '2025-08-03T19:23:45.380856', 'entity_extraction_analysis_id': 180}
2025-08-03 19:23:45,581 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:23:45,581 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:23:45][EntityExtractor][180][************************************] INFO: Updated analysis 180 status to IN_PROGRESS
2025-08-03 19:23:45,731 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:23:45,731 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:23:45,731 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:23:45,731 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:23:45,731 INFO sqlalchemy.engine.Engine [cached since 2.458s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:23:45.681618', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Updated analysis 180 status to IN_PROGRESS", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:23:45.681608", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:23:45,731 - sqlalchemy.engine.Engine - INFO - [cached since 2.458s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:23:45.681618', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Updated analysis 180 status to IN_PROGRESS", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:23:45.681608", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:23:45,790 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:23:45,790 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:23:45][EntityExtractor][url_retrieval_************************************][************************************] INFO: Retrieving policy URLs with reachability status
2025-08-03 19:23:45,940 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:23:45,940 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:23:45,941 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:23:45,941 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:23:45,941 INFO sqlalchemy.engine.Engine [cached since 2.667s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:23:45.889833', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Retrieving policy URLs with reachability status", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:23:45.889824", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:23:45,941 - sqlalchemy.engine.Engine - INFO - [cached since 2.667s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:23:45.889833', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Retrieving policy URLs with reachability status", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:23:45.889824", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:23:46,000 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:23:46,000 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:23:46][EntityExtractor][url_retrieval_************************************][************************************] INFO: Found latest scrape_request_ref_id: ************************************ for domain: shell.in
2025-08-03 19:23:47,066 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:23:47,066 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:23:47,066 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:23:47,066 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:23:47,066 INFO sqlalchemy.engine.Engine [cached since 3.792s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:23:46.988953', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found latest scrape_request_ref_id: ************************************ for domain: shell.in", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:23:46.988943", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:23:47,066 - sqlalchemy.engine.Engine - INFO - [cached since 3.792s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:23:46.988953', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found latest scrape_request_ref_id: ************************************ for domain: shell.in", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:23:46.988943", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:23:47,132 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:23:47,132 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:23:47][EntityExtractor][url_retrieval_************************************][************************************] INFO: MCC only provided 0 policy types, force-loading 5 missing policies from policy_analysis_new_gemini
2025-08-03 19:23:47,601 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:23:47,601 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:23:47,601 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:23:47,601 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:23:47,601 INFO sqlalchemy.engine.Engine [cached since 4.328s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:23:47.549815', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "MCC only provided 0 policy types, force-loading 5 missing policies from policy_analysis_new_gemini", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:23:47.549807", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:23:47,601 - sqlalchemy.engine.Engine - INFO - [cached since 4.328s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:23:47.549815', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "MCC only provided 0 policy types, force-loading 5 missing policies from policy_analysis_new_gemini", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:23:47.549807", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:23:47,670 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:23:47,670 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:23:47][EntityExtractor][url_retrieval_************************************][************************************] INFO: Found latest scrape_request_ref_id: ************************************ for domain: shell.in
2025-08-03 19:23:48,051 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:23:48,051 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:23:48,051 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:23:48,051 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:23:48,051 INFO sqlalchemy.engine.Engine [cached since 4.777s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:23:47.990927', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found latest scrape_request_ref_id: ************************************ for domain: shell.in", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:23:47.990917", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:23:48,051 - sqlalchemy.engine.Engine - INFO - [cached since 4.777s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:23:47.990927', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found latest scrape_request_ref_id: ************************************ for domain: shell.in", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:23:47.990917", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:23:48,112 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:23:48,112 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:23:48][EntityExtractor][url_retrieval_************************************][************************************] INFO: Retrieved policy analysis data for 6 policy types
2025-08-03 19:23:48,545 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:23:48,545 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:23:48,545 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:23:48,545 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:23:48,545 INFO sqlalchemy.engine.Engine [cached since 5.271s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:23:48.495916', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Retrieved policy analysis data for 6 policy types", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:23:48.495907", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:23:48,545 - sqlalchemy.engine.Engine - INFO - [cached since 5.271s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:23:48.495916', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Retrieved policy analysis data for 6 policy types", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:23:48.495907", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:23:48,604 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:23:48,604 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:23:48][EntityExtractor][url_retrieval_************************************][************************************] INFO: Force-loaded 5 policies from policy_analysis_new_gemini as unreachable
2025-08-03 19:23:48,869 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:23:48,869 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:23:48,869 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:23:48,869 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:23:48,869 INFO sqlalchemy.engine.Engine [cached since 5.596s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:23:48.805550', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Force-loaded 5 policies from policy_analysis_new_gemini as unreachable", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:23:48.805541", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:23:48,869 - sqlalchemy.engine.Engine - INFO - [cached since 5.596s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:23:48.805550', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Force-loaded 5 policies from policy_analysis_new_gemini as unreachable", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:23:48.805541", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:23:48,931 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:23:48,931 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:23:49][EntityExtractor][url_retrieval_************************************][************************************] INFO: Total policy URLs retrieved: 5
2025-08-03 19:23:49,132 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:23:49,132 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:23:49,133 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:23:49,133 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:23:49,133 INFO sqlalchemy.engine.Engine [cached since 5.859s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:23:49.067729', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Total policy URLs retrieved: 5", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:23:49.067720", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:23:49,133 - sqlalchemy.engine.Engine - INFO - [cached since 5.859s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:23:49.067729', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Total policy URLs retrieved: 5", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:23:49.067720", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:23:49,191 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:23:49,191 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:23:49][EntityExtractor][url_retrieval_************************************][************************************] INFO: Found latest scrape_request_ref_id: ************************************ for domain: shell.in
2025-08-03 19:23:49,577 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:23:49,577 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:23:49,577 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:23:49,577 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:23:49,577 INFO sqlalchemy.engine.Engine [cached since 6.303s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:23:49.505131', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found latest scrape_request_ref_id: ************************************ for domain: shell.in", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:23:49.505122", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:23:49,577 - sqlalchemy.engine.Engine - INFO - [cached since 6.303s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:23:49.505131', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found latest scrape_request_ref_id: ************************************ for domain: shell.in", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:23:49.505122", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:23:49,641 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:23:49,641 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:23:50][EntityExtractor][url_retrieval_************************************][************************************] INFO: Retrieved policy analysis data for 6 policy types
2025-08-03 19:23:50,072 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:23:50,072 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:23:50,072 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:23:50,072 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:23:50,072 INFO sqlalchemy.engine.Engine [cached since 6.799s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:23:50.017331', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Retrieved policy analysis data for 6 policy types", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:23:50.017323", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:23:50,072 - sqlalchemy.engine.Engine - INFO - [cached since 6.799s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:23:50.017331', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Retrieved policy analysis data for 6 policy types", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:23:50.017323", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:23:50,132 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:23:50,132 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:23:50][EntityExtractor][180][************************************] INFO: Filtered policy URLs keys: ['privacy_policy', 'terms_and_condition', 'shipping_delivery', 'contact_us', 'about_us']
2025-08-03 19:23:50,401 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:23:50,401 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:23:50,401 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:23:50,401 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:23:50,401 INFO sqlalchemy.engine.Engine [cached since 7.128s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:23:50.349323', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Filtered policy URLs keys: [\'privacy_policy\', \'terms_and_condition\', \'shipping_delivery\', \'contact_us\', \'about_us\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:23:50.349311", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:23:50,401 - sqlalchemy.engine.Engine - INFO - [cached since 7.128s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:23:50.349323', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Filtered policy URLs keys: [\'privacy_policy\', \'terms_and_condition\', \'shipping_delivery\', \'contact_us\', \'about_us\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:23:50.349311", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:23:50,471 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:23:50,471 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:23:50][EntityExtractor][180][************************************] INFO: Extracted text length for privacy_policy: 2340
2025-08-03 19:23:50,649 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:23:50,649 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:23:50,649 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:23:50,649 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:23:50,649 INFO sqlalchemy.engine.Engine [cached since 7.376s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:23:50.593300', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Extracted text length for privacy_policy: 2340", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:23:50.593291", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:23:50,649 - sqlalchemy.engine.Engine - INFO - [cached since 7.376s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:23:50.593300', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Extracted text length for privacy_policy: 2340", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:23:50.593291", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:23:50,711 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:23:50,711 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:23:50][EntityExtractor][180][************************************] INFO: Extracted text length for terms_and_condition: 1479
2025-08-03 19:23:50,907 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:23:50,907 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:23:50,907 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:23:50,907 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:23:50,907 INFO sqlalchemy.engine.Engine [cached since 7.633s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:23:50.825740', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Extracted text length for terms_and_condition: 1479", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:23:50.825730", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:23:50,907 - sqlalchemy.engine.Engine - INFO - [cached since 7.633s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:23:50.825740', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Extracted text length for terms_and_condition: 1479", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:23:50.825730", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:23:50,980 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:23:50,980 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:23:51][EntityExtractor][url_retrieval_************************************][************************************] INFO: Checking Gemini reachability for 5 URLs
2025-08-03 19:23:51,127 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:23:51,127 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:23:51,127 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:23:51,127 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:23:51,127 INFO sqlalchemy.engine.Engine [cached since 7.854s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:23:51.075979', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Checking Gemini reachability for 5 URLs", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:23:51.075969", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:23:51,127 - sqlalchemy.engine.Engine - INFO - [cached since 7.854s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:23:51.075979', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Checking Gemini reachability for 5 URLs", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:23:51.075969", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:23:51,191 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:23:51,191 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:23:51][EntityExtractor][url_retrieval_************************************][************************************] INFO: Gemini reachability check completed: 5 reachable, 0 unreachable
2025-08-03 19:23:51,353 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:23:51,353 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:23:51,354 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:23:51,354 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:23:51,354 INFO sqlalchemy.engine.Engine [cached since 8.08s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:23:51.291733', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Gemini reachability check completed: 5 reachable, 0 unreachable", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:23:51.291722", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:23:51,354 - sqlalchemy.engine.Engine - INFO - [cached since 8.08s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:23:51.291733', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Gemini reachability check completed: 5 reachable, 0 unreachable", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:23:51.291722", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:23:51,419 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:23:51,419 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 19:23:53,575 - google_genai.models - INFO - AFC is enabled with max remote calls: 20000.
2025-08-03 19:24:11,994 - httpx - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent "HTTP/1.1 200 OK"
2025-08-03 19:24:11,995 - google_genai.models - INFO - AFC remote call 1 is done.
[2025-08-03 19:23:53][legacy_unknown][unknown] INFO: Starting Gemini API call
{
  "model": "gemini-2.5-flash",
  "timeout": 120,
  "max_retries": 3,
  "prompt_length": 5395,
  "context": {
    "task_type": "legacy"
  }
}
[2025-08-03 19:23:53][legacy_unknown][unknown] INFO: Gemini API attempt 1/3
[2025-08-03 19:24:11][legacy_unknown][unknown] INFO: Gemini API Usage: cache_tokens_details=None cached_content_token_count=None candidates_token_count=310 candidates_tokens_details=None prompt_token_count=1281 prompt_tokens_details=[ModalityTokenCount(
  modality=<MediaModality.TEXT: 'TEXT'>,
  token_count=1281
)] thoughts_token_count=2112 tool_use_prompt_token_count=None tool_use_prompt_tokens_details=None total_token_count=3703 traffic_type=None
[2025-08-03 19:24:11][legacy_unknown][unknown] INFO: Gemini API call successful
{
  "attempt": 1,
  "response_length": 655,
  "finish_reason": "STOP"
}
[2025-08-03 19:24:14][api_response_logger][unknown] INFO: Gemini API Call - Model: gemini-2.5-flash, Request ID: None
{
  "response_preview": "```json\n{\n    \"legal_name\": \"Shell India Markets Private Limited\",\n    \"business_email\": [\"<EMAIL>\"],\n    \"support_email\": [],\n    \"business_contact_numbers\": [\"044-3099 11...",
  "log_file": "api_logs/gemini_20250803_192413_997.json",
  "clean_file": "api_logs/clean_gemini_20250803_192413_997.json"
}
[API_LOGGER] Gemini response logged to: api_logs/gemini_20250803_192413_997.json
[API_LOGGER] Clean version at: api_logs/clean_gemini_20250803_192413_997.json
[2025-08-03 19:24:14][api_response_logger][unknown] INFO: Gemini API Call - Model: gemini-2.5-flash, Request ID: ************************************
{
  "response_preview": "```json\n{\n    \"legal_name\": \"Shell India Markets Private Limited\",\n    \"business_email\": [\"<EMAIL>\"],\n    \"support_email\": [],\n    \"business_contact_numbers\": [\"044-3099 11...",
  "log_file": "api_logs/gemini_20250803_192414_008_************************************.json",
  "clean_file": "api_logs/clean_gemini_20250803_192414_008_************************************.json"
}
[API_LOGGER] Gemini response logged to: api_logs/gemini_20250803_192414_008_************************************.json
[API_LOGGER] Clean version at: api_logs/clean_gemini_20250803_192414_008_************************************.json
2025-08-03 19:24:14,068 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:24:14,068 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:24:14,069 INFO sqlalchemy.engine.Engine SELECT entity_extraction_url_analysis.id, entity_extraction_url_analysis.analysis_id, entity_extraction_url_analysis.entity_analysis_id, entity_extraction_url_analysis.scrape_request_ref_id, entity_extraction_url_analysis.url, entity_extraction_url_analysis.url_type, entity_extraction_url_analysis.url_classification, entity_extraction_url_analysis.url_source_table, entity_extraction_url_analysis.is_reachable_by_gemini, entity_extraction_url_analysis.soft_classification, entity_extraction_url_analysis.hard_classification, entity_extraction_url_analysis.extracted_text, entity_extraction_url_analysis.extracted_text_length, entity_extraction_url_analysis.extracted_entities, entity_extraction_url_analysis.processing_method, entity_extraction_url_analysis.processing_status, entity_extraction_url_analysis.error_message, entity_extraction_url_analysis.created_at, entity_extraction_url_analysis.processed_at, entity_extraction_url_analysis.org_id 
FROM entity_extraction_url_analysis 
WHERE entity_extraction_url_analysis.analysis_id = %(analysis_id_1)s AND entity_extraction_url_analysis.url = %(url_1)s
2025-08-03 19:24:14,069 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_url_analysis.id, entity_extraction_url_analysis.analysis_id, entity_extraction_url_analysis.entity_analysis_id, entity_extraction_url_analysis.scrape_request_ref_id, entity_extraction_url_analysis.url, entity_extraction_url_analysis.url_type, entity_extraction_url_analysis.url_classification, entity_extraction_url_analysis.url_source_table, entity_extraction_url_analysis.is_reachable_by_gemini, entity_extraction_url_analysis.soft_classification, entity_extraction_url_analysis.hard_classification, entity_extraction_url_analysis.extracted_text, entity_extraction_url_analysis.extracted_text_length, entity_extraction_url_analysis.extracted_entities, entity_extraction_url_analysis.processing_method, entity_extraction_url_analysis.processing_status, entity_extraction_url_analysis.error_message, entity_extraction_url_analysis.created_at, entity_extraction_url_analysis.processed_at, entity_extraction_url_analysis.org_id 
FROM entity_extraction_url_analysis 
WHERE entity_extraction_url_analysis.analysis_id = %(analysis_id_1)s AND entity_extraction_url_analysis.url = %(url_1)s
2025-08-03 19:24:14,069 INFO sqlalchemy.engine.Engine [generated in 0.00013s] {'analysis_id_1': 180, 'url_1': 'https://www.shell.in/privacy.html'}
2025-08-03 19:24:14,069 - sqlalchemy.engine.Engine - INFO - [generated in 0.00013s] {'analysis_id_1': 180, 'url_1': 'https://www.shell.in/privacy.html'}
[2025-08-03 19:24:14][EntityExtractor][None][************************************] WARNING: URL analysis record not found for URL: https://www.shell.in/privacy.html
2025-08-03 19:24:14,191 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:24:14,191 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:24:14,191 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:14,191 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:14,191 INFO sqlalchemy.engine.Engine [cached since 30.92s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:24:14.136529', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "URL analysis record not found for URL: https://www.shell.in/privacy.html", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:24:14.136523", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:14,191 - sqlalchemy.engine.Engine - INFO - [cached since 30.92s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:24:14.136529', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "URL analysis record not found for URL: https://www.shell.in/privacy.html", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:24:14.136523", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:14,251 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:24:14,251 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 19:24:14,356 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-03 19:24:14,356 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-03 19:24:14,526 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:24:14,526 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:24:14,526 INFO sqlalchemy.engine.Engine SELECT entity_extraction_url_analysis.id, entity_extraction_url_analysis.analysis_id, entity_extraction_url_analysis.entity_analysis_id, entity_extraction_url_analysis.scrape_request_ref_id, entity_extraction_url_analysis.url, entity_extraction_url_analysis.url_type, entity_extraction_url_analysis.url_classification, entity_extraction_url_analysis.url_source_table, entity_extraction_url_analysis.is_reachable_by_gemini, entity_extraction_url_analysis.soft_classification, entity_extraction_url_analysis.hard_classification, entity_extraction_url_analysis.extracted_text, entity_extraction_url_analysis.extracted_text_length, entity_extraction_url_analysis.extracted_entities, entity_extraction_url_analysis.processing_method, entity_extraction_url_analysis.processing_status, entity_extraction_url_analysis.error_message, entity_extraction_url_analysis.created_at, entity_extraction_url_analysis.processed_at, entity_extraction_url_analysis.org_id 
FROM entity_extraction_url_analysis 
WHERE entity_extraction_url_analysis.analysis_id = %(analysis_id_1)s AND entity_extraction_url_analysis.url = %(url_1)s
2025-08-03 19:24:14,526 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_url_analysis.id, entity_extraction_url_analysis.analysis_id, entity_extraction_url_analysis.entity_analysis_id, entity_extraction_url_analysis.scrape_request_ref_id, entity_extraction_url_analysis.url, entity_extraction_url_analysis.url_type, entity_extraction_url_analysis.url_classification, entity_extraction_url_analysis.url_source_table, entity_extraction_url_analysis.is_reachable_by_gemini, entity_extraction_url_analysis.soft_classification, entity_extraction_url_analysis.hard_classification, entity_extraction_url_analysis.extracted_text, entity_extraction_url_analysis.extracted_text_length, entity_extraction_url_analysis.extracted_entities, entity_extraction_url_analysis.processing_method, entity_extraction_url_analysis.processing_status, entity_extraction_url_analysis.error_message, entity_extraction_url_analysis.created_at, entity_extraction_url_analysis.processed_at, entity_extraction_url_analysis.org_id 
FROM entity_extraction_url_analysis 
WHERE entity_extraction_url_analysis.analysis_id = %(analysis_id_1)s AND entity_extraction_url_analysis.url = %(url_1)s
2025-08-03 19:24:14,526 INFO sqlalchemy.engine.Engine [cached since 0.4568s ago] {'analysis_id_1': 180, 'url_1': 'https://www.shell.in/terms-and-conditions.html'}
2025-08-03 19:24:14,526 - sqlalchemy.engine.Engine - INFO - [cached since 0.4568s ago] {'analysis_id_1': 180, 'url_1': 'https://www.shell.in/terms-and-conditions.html'}
[2025-08-03 19:24:14][EntityExtractor][None][************************************] WARNING: URL analysis record not found for URL: https://www.shell.in/terms-and-conditions.html
2025-08-03 19:24:14,658 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:24:14,658 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:24:14,658 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:14,658 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:14,658 INFO sqlalchemy.engine.Engine [cached since 31.38s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:24:14.593848', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "URL analysis record not found for URL: https://www.shell.in/terms-and-conditions.html", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:24:14.593839", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:14,658 - sqlalchemy.engine.Engine - INFO - [cached since 31.38s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:24:14.593848', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "URL analysis record not found for URL: https://www.shell.in/terms-and-conditions.html", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:24:14.593839", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:14,741 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:24:14,741 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 19:24:14,858 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-03 19:24:14,858 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-03 19:24:15,053 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:24:15,053 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:24:15,053 INFO sqlalchemy.engine.Engine SELECT entity_extraction_url_analysis.id, entity_extraction_url_analysis.analysis_id, entity_extraction_url_analysis.entity_analysis_id, entity_extraction_url_analysis.scrape_request_ref_id, entity_extraction_url_analysis.url, entity_extraction_url_analysis.url_type, entity_extraction_url_analysis.url_classification, entity_extraction_url_analysis.url_source_table, entity_extraction_url_analysis.is_reachable_by_gemini, entity_extraction_url_analysis.soft_classification, entity_extraction_url_analysis.hard_classification, entity_extraction_url_analysis.extracted_text, entity_extraction_url_analysis.extracted_text_length, entity_extraction_url_analysis.extracted_entities, entity_extraction_url_analysis.processing_method, entity_extraction_url_analysis.processing_status, entity_extraction_url_analysis.error_message, entity_extraction_url_analysis.created_at, entity_extraction_url_analysis.processed_at, entity_extraction_url_analysis.org_id 
FROM entity_extraction_url_analysis 
WHERE entity_extraction_url_analysis.analysis_id = %(analysis_id_1)s AND entity_extraction_url_analysis.url = %(url_1)s
2025-08-03 19:24:15,053 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_url_analysis.id, entity_extraction_url_analysis.analysis_id, entity_extraction_url_analysis.entity_analysis_id, entity_extraction_url_analysis.scrape_request_ref_id, entity_extraction_url_analysis.url, entity_extraction_url_analysis.url_type, entity_extraction_url_analysis.url_classification, entity_extraction_url_analysis.url_source_table, entity_extraction_url_analysis.is_reachable_by_gemini, entity_extraction_url_analysis.soft_classification, entity_extraction_url_analysis.hard_classification, entity_extraction_url_analysis.extracted_text, entity_extraction_url_analysis.extracted_text_length, entity_extraction_url_analysis.extracted_entities, entity_extraction_url_analysis.processing_method, entity_extraction_url_analysis.processing_status, entity_extraction_url_analysis.error_message, entity_extraction_url_analysis.created_at, entity_extraction_url_analysis.processed_at, entity_extraction_url_analysis.org_id 
FROM entity_extraction_url_analysis 
WHERE entity_extraction_url_analysis.analysis_id = %(analysis_id_1)s AND entity_extraction_url_analysis.url = %(url_1)s
2025-08-03 19:24:15,053 INFO sqlalchemy.engine.Engine [cached since 0.9841s ago] {'analysis_id_1': 180, 'url_1': 'not_found'}
2025-08-03 19:24:15,053 - sqlalchemy.engine.Engine - INFO - [cached since 0.9841s ago] {'analysis_id_1': 180, 'url_1': 'not_found'}
[2025-08-03 19:24:15][EntityExtractor][None][************************************] WARNING: URL analysis record not found for URL: not_found
2025-08-03 19:24:15,186 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:24:15,186 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:24:15,186 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:15,186 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:15,186 INFO sqlalchemy.engine.Engine [cached since 31.91s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:24:15.132941', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "URL analysis record not found for URL: not_found", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:24:15.132934", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:15,186 - sqlalchemy.engine.Engine - INFO - [cached since 31.91s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:24:15.132941', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "URL analysis record not found for URL: not_found", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:24:15.132934", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:15,254 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:24:15,254 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 19:24:15,361 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-03 19:24:15,361 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-03 19:24:15,521 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:24:15,521 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:24:15,521 INFO sqlalchemy.engine.Engine SELECT entity_extraction_url_analysis.id, entity_extraction_url_analysis.analysis_id, entity_extraction_url_analysis.entity_analysis_id, entity_extraction_url_analysis.scrape_request_ref_id, entity_extraction_url_analysis.url, entity_extraction_url_analysis.url_type, entity_extraction_url_analysis.url_classification, entity_extraction_url_analysis.url_source_table, entity_extraction_url_analysis.is_reachable_by_gemini, entity_extraction_url_analysis.soft_classification, entity_extraction_url_analysis.hard_classification, entity_extraction_url_analysis.extracted_text, entity_extraction_url_analysis.extracted_text_length, entity_extraction_url_analysis.extracted_entities, entity_extraction_url_analysis.processing_method, entity_extraction_url_analysis.processing_status, entity_extraction_url_analysis.error_message, entity_extraction_url_analysis.created_at, entity_extraction_url_analysis.processed_at, entity_extraction_url_analysis.org_id 
FROM entity_extraction_url_analysis 
WHERE entity_extraction_url_analysis.analysis_id = %(analysis_id_1)s AND entity_extraction_url_analysis.url = %(url_1)s
2025-08-03 19:24:15,521 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_url_analysis.id, entity_extraction_url_analysis.analysis_id, entity_extraction_url_analysis.entity_analysis_id, entity_extraction_url_analysis.scrape_request_ref_id, entity_extraction_url_analysis.url, entity_extraction_url_analysis.url_type, entity_extraction_url_analysis.url_classification, entity_extraction_url_analysis.url_source_table, entity_extraction_url_analysis.is_reachable_by_gemini, entity_extraction_url_analysis.soft_classification, entity_extraction_url_analysis.hard_classification, entity_extraction_url_analysis.extracted_text, entity_extraction_url_analysis.extracted_text_length, entity_extraction_url_analysis.extracted_entities, entity_extraction_url_analysis.processing_method, entity_extraction_url_analysis.processing_status, entity_extraction_url_analysis.error_message, entity_extraction_url_analysis.created_at, entity_extraction_url_analysis.processed_at, entity_extraction_url_analysis.org_id 
FROM entity_extraction_url_analysis 
WHERE entity_extraction_url_analysis.analysis_id = %(analysis_id_1)s AND entity_extraction_url_analysis.url = %(url_1)s
2025-08-03 19:24:15,521 INFO sqlalchemy.engine.Engine [cached since 1.452s ago] {'analysis_id_1': 180, 'url_1': 'https://www.shell.in/about-us/contact-us.html'}
2025-08-03 19:24:15,521 - sqlalchemy.engine.Engine - INFO - [cached since 1.452s ago] {'analysis_id_1': 180, 'url_1': 'https://www.shell.in/about-us/contact-us.html'}
[2025-08-03 19:24:15][EntityExtractor][None][************************************] WARNING: URL analysis record not found for URL: https://www.shell.in/about-us/contact-us.html
2025-08-03 19:24:15,642 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:24:15,642 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:24:15,642 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:15,642 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:15,642 INFO sqlalchemy.engine.Engine [cached since 32.37s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:24:15.589764', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "URL analysis record not found for URL: https://www.shell.in/about-us/contact-us.html", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:24:15.589758", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:15,642 - sqlalchemy.engine.Engine - INFO - [cached since 32.37s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:24:15.589764', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "URL analysis record not found for URL: https://www.shell.in/about-us/contact-us.html", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:24:15.589758", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:15,701 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:24:15,701 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 19:24:15,804 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-03 19:24:15,804 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-03 19:24:15,951 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:24:15,951 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:24:15,951 INFO sqlalchemy.engine.Engine SELECT entity_extraction_url_analysis.id, entity_extraction_url_analysis.analysis_id, entity_extraction_url_analysis.entity_analysis_id, entity_extraction_url_analysis.scrape_request_ref_id, entity_extraction_url_analysis.url, entity_extraction_url_analysis.url_type, entity_extraction_url_analysis.url_classification, entity_extraction_url_analysis.url_source_table, entity_extraction_url_analysis.is_reachable_by_gemini, entity_extraction_url_analysis.soft_classification, entity_extraction_url_analysis.hard_classification, entity_extraction_url_analysis.extracted_text, entity_extraction_url_analysis.extracted_text_length, entity_extraction_url_analysis.extracted_entities, entity_extraction_url_analysis.processing_method, entity_extraction_url_analysis.processing_status, entity_extraction_url_analysis.error_message, entity_extraction_url_analysis.created_at, entity_extraction_url_analysis.processed_at, entity_extraction_url_analysis.org_id 
FROM entity_extraction_url_analysis 
WHERE entity_extraction_url_analysis.analysis_id = %(analysis_id_1)s AND entity_extraction_url_analysis.url = %(url_1)s
2025-08-03 19:24:15,951 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_url_analysis.id, entity_extraction_url_analysis.analysis_id, entity_extraction_url_analysis.entity_analysis_id, entity_extraction_url_analysis.scrape_request_ref_id, entity_extraction_url_analysis.url, entity_extraction_url_analysis.url_type, entity_extraction_url_analysis.url_classification, entity_extraction_url_analysis.url_source_table, entity_extraction_url_analysis.is_reachable_by_gemini, entity_extraction_url_analysis.soft_classification, entity_extraction_url_analysis.hard_classification, entity_extraction_url_analysis.extracted_text, entity_extraction_url_analysis.extracted_text_length, entity_extraction_url_analysis.extracted_entities, entity_extraction_url_analysis.processing_method, entity_extraction_url_analysis.processing_status, entity_extraction_url_analysis.error_message, entity_extraction_url_analysis.created_at, entity_extraction_url_analysis.processed_at, entity_extraction_url_analysis.org_id 
FROM entity_extraction_url_analysis 
WHERE entity_extraction_url_analysis.analysis_id = %(analysis_id_1)s AND entity_extraction_url_analysis.url = %(url_1)s
2025-08-03 19:24:15,951 INFO sqlalchemy.engine.Engine [cached since 1.882s ago] {'analysis_id_1': 180, 'url_1': 'https://www.shell.in/about-us/careers.html'}
2025-08-03 19:24:15,951 - sqlalchemy.engine.Engine - INFO - [cached since 1.882s ago] {'analysis_id_1': 180, 'url_1': 'https://www.shell.in/about-us/careers.html'}
[2025-08-03 19:24:16][EntityExtractor][None][************************************] WARNING: URL analysis record not found for URL: https://www.shell.in/about-us/careers.html
2025-08-03 19:24:16,061 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:24:16,061 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:24:16,061 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:16,061 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:16,061 INFO sqlalchemy.engine.Engine [cached since 32.79s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:24:16.011934', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "URL analysis record not found for URL: https://www.shell.in/about-us/careers.html", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:24:16.011927", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:16,061 - sqlalchemy.engine.Engine - INFO - [cached since 32.79s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:24:16.011934', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "URL analysis record not found for URL: https://www.shell.in/about-us/careers.html", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:24:16.011927", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:16,129 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:24:16,129 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 19:24:16,259 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-03 19:24:16,259 - sqlalchemy.engine.Engine - INFO - ROLLBACK
[2025-08-03 19:24:16][EntityExtractor][180][************************************] INFO: Gemini extraction rate 63.6% < 90%, forcing backup flow for all fields
2025-08-03 19:24:16,418 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:24:16,418 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:24:16,418 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:16,418 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:16,418 INFO sqlalchemy.engine.Engine [cached since 33.14s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:24:16.361333', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Gemini extraction rate 63.6% < 90%, forcing backup flow for all fields", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:24:16.361325", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:16,418 - sqlalchemy.engine.Engine - INFO - [cached since 33.14s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:24:16.361333', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Gemini extraction rate 63.6% < 90%, forcing backup flow for all fields", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:24:16.361325", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:16,481 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:24:16,481 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:24:16][EntityExtractor][180][************************************] INFO: Gemini missing fields to fallback: ['support_email', 'accepts_international_orders', 'shipping_countries', 'shipping_policy_details']
2025-08-03 19:24:16,631 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:24:16,631 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:24:16,631 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:16,631 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:16,631 INFO sqlalchemy.engine.Engine [cached since 33.36s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:24:16.582409', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Gemini missing fields to fallback: [\'support_email\', \'accepts_international_orders\', \'shipping_countries\', \'shipping_policy_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:24:16.582400", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:16,631 - sqlalchemy.engine.Engine - INFO - [cached since 33.36s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:24:16.582409', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Gemini missing fields to fallback: [\'support_email\', \'accepts_international_orders\', \'shipping_countries\', \'shipping_policy_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:24:16.582400", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:16,701 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:24:16,701 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:24:16][EntityExtractor][180][************************************] INFO: Starting OpenAI backup flow for missing fields: ['support_email', 'accepts_international_orders', 'shipping_countries', 'shipping_policy_details']
2025-08-03 19:24:16,851 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:24:16,851 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:24:16,851 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:16,851 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:16,851 INFO sqlalchemy.engine.Engine [cached since 33.58s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:24:16.802404', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Starting OpenAI backup flow for missing fields: [\'support_email\', \'accepts_international_orders\', \'shipping_countr ... (8 characters truncated) ... 'shipping_policy_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:24:16.802396", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:16,851 - sqlalchemy.engine.Engine - INFO - [cached since 33.58s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:24:16.802404', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Starting OpenAI backup flow for missing fields: [\'support_email\', \'accepts_international_orders\', \'shipping_countr ... (8 characters truncated) ... 'shipping_policy_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:24:16.802396", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:16,911 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:24:16,911 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 19:24:17,060 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:24:17,060 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:24:17,065 INFO sqlalchemy.engine.Engine SELECT policy_analysis_new_gemini.id, policy_analysis_new_gemini.website, policy_analysis_new_gemini.scrape_request_ref_id, policy_analysis_new_gemini.analysis_flow_used, policy_analysis_new_gemini.reachability_percentage, policy_analysis_new_gemini.total_urls_processed, policy_analysis_new_gemini.privacy_policy_url, policy_analysis_new_gemini.terms_and_condition_url, policy_analysis_new_gemini.shipping_delivery_url, policy_analysis_new_gemini.returns_cancellation_exchange_url, policy_analysis_new_gemini.contact_us_url, policy_analysis_new_gemini.home_page_url, policy_analysis_new_gemini.about_us_url, policy_analysis_new_gemini.privacy_policy_text, policy_analysis_new_gemini.terms_and_condition_text, policy_analysis_new_gemini.shipping_delivery_text, policy_analysis_new_gemini.returns_cancellation_exchange_text, policy_analysis_new_gemini.contact_us_text, policy_analysis_new_gemini.home_page_text, policy_analysis_new_gemini.about_us_text, policy_analysis_new_gemini.privacy_policy_screenshot, policy_analysis_new_gemini.terms_and_condition_screenshot, policy_analysis_new_gemini.shipping_delivery_screenshot, policy_analysis_new_gemini.returns_cancellation_exchange_screenshot, policy_analysis_new_gemini.contact_us_screenshot, policy_analysis_new_gemini.home_page_screenshot, policy_analysis_new_gemini.about_us_screenshot, policy_analysis_new_gemini.instagram_url, policy_analysis_new_gemini.instagram_text, policy_analysis_new_gemini.instagram_screenshot, policy_analysis_new_gemini.youtube_url, policy_analysis_new_gemini.youtube_text, policy_analysis_new_gemini.youtube_screenshot, policy_analysis_new_gemini.facebook_url, policy_analysis_new_gemini.facebook_text, policy_analysis_new_gemini.facebook_screenshot, policy_analysis_new_gemini.twitter_url, policy_analysis_new_gemini.twitter_text, policy_analysis_new_gemini.twitter_screenshot, policy_analysis_new_gemini.linkedin_url, policy_analysis_new_gemini.linkedin_text, policy_analysis_new_gemini.linkedin_screenshot, policy_analysis_new_gemini.pinterest_url, policy_analysis_new_gemini.pinterest_text, policy_analysis_new_gemini.pinterest_screenshot, policy_analysis_new_gemini.x_url, policy_analysis_new_gemini.x_text, policy_analysis_new_gemini.x_screenshot, policy_analysis_new_gemini.result_status, policy_analysis_new_gemini.failed_at, policy_analysis_new_gemini.last_updated, policy_analysis_new_gemini.details, policy_analysis_new_gemini.processing_status, policy_analysis_new_gemini.created_at, policy_analysis_new_gemini.started_at, policy_analysis_new_gemini.completed_at, policy_analysis_new_gemini.error_message, policy_analysis_new_gemini.org_id 
FROM policy_analysis_new_gemini 
WHERE policy_analysis_new_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s ORDER BY policy_analysis_new_gemini.id DESC
2025-08-03 19:24:17,065 - sqlalchemy.engine.Engine - INFO - SELECT policy_analysis_new_gemini.id, policy_analysis_new_gemini.website, policy_analysis_new_gemini.scrape_request_ref_id, policy_analysis_new_gemini.analysis_flow_used, policy_analysis_new_gemini.reachability_percentage, policy_analysis_new_gemini.total_urls_processed, policy_analysis_new_gemini.privacy_policy_url, policy_analysis_new_gemini.terms_and_condition_url, policy_analysis_new_gemini.shipping_delivery_url, policy_analysis_new_gemini.returns_cancellation_exchange_url, policy_analysis_new_gemini.contact_us_url, policy_analysis_new_gemini.home_page_url, policy_analysis_new_gemini.about_us_url, policy_analysis_new_gemini.privacy_policy_text, policy_analysis_new_gemini.terms_and_condition_text, policy_analysis_new_gemini.shipping_delivery_text, policy_analysis_new_gemini.returns_cancellation_exchange_text, policy_analysis_new_gemini.contact_us_text, policy_analysis_new_gemini.home_page_text, policy_analysis_new_gemini.about_us_text, policy_analysis_new_gemini.privacy_policy_screenshot, policy_analysis_new_gemini.terms_and_condition_screenshot, policy_analysis_new_gemini.shipping_delivery_screenshot, policy_analysis_new_gemini.returns_cancellation_exchange_screenshot, policy_analysis_new_gemini.contact_us_screenshot, policy_analysis_new_gemini.home_page_screenshot, policy_analysis_new_gemini.about_us_screenshot, policy_analysis_new_gemini.instagram_url, policy_analysis_new_gemini.instagram_text, policy_analysis_new_gemini.instagram_screenshot, policy_analysis_new_gemini.youtube_url, policy_analysis_new_gemini.youtube_text, policy_analysis_new_gemini.youtube_screenshot, policy_analysis_new_gemini.facebook_url, policy_analysis_new_gemini.facebook_text, policy_analysis_new_gemini.facebook_screenshot, policy_analysis_new_gemini.twitter_url, policy_analysis_new_gemini.twitter_text, policy_analysis_new_gemini.twitter_screenshot, policy_analysis_new_gemini.linkedin_url, policy_analysis_new_gemini.linkedin_text, policy_analysis_new_gemini.linkedin_screenshot, policy_analysis_new_gemini.pinterest_url, policy_analysis_new_gemini.pinterest_text, policy_analysis_new_gemini.pinterest_screenshot, policy_analysis_new_gemini.x_url, policy_analysis_new_gemini.x_text, policy_analysis_new_gemini.x_screenshot, policy_analysis_new_gemini.result_status, policy_analysis_new_gemini.failed_at, policy_analysis_new_gemini.last_updated, policy_analysis_new_gemini.details, policy_analysis_new_gemini.processing_status, policy_analysis_new_gemini.created_at, policy_analysis_new_gemini.started_at, policy_analysis_new_gemini.completed_at, policy_analysis_new_gemini.error_message, policy_analysis_new_gemini.org_id 
FROM policy_analysis_new_gemini 
WHERE policy_analysis_new_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s ORDER BY policy_analysis_new_gemini.id DESC
2025-08-03 19:24:17,066 INFO sqlalchemy.engine.Engine [generated in 0.00051s] {'scrape_request_ref_id_1': '************************************'}
2025-08-03 19:24:17,066 - sqlalchemy.engine.Engine - INFO - [generated in 0.00051s] {'scrape_request_ref_id_1': '************************************'}
[2025-08-03 19:24:17][EntityExtractor][180][************************************] INFO: Retrieved policy texts: ['privacy_policy', 'terms_and_condition', 'contact_us', 'about_us']
2025-08-03 19:24:17,582 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:24:17,582 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:24:17,582 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:17,582 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:17,582 INFO sqlalchemy.engine.Engine [cached since 34.31s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:24:17.221632', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Retrieved policy texts: [\'privacy_policy\', \'terms_and_condition\', \'contact_us\', \'about_us\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:24:17.221618", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:17,582 - sqlalchemy.engine.Engine - INFO - [cached since 34.31s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:24:17.221632', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Retrieved policy texts: [\'privacy_policy\', \'terms_and_condition\', \'contact_us\', \'about_us\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:24:17.221618", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:17,642 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:24:17,642 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 19:24:17,761 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-03 19:24:17,761 - sqlalchemy.engine.Engine - INFO - ROLLBACK
[2025-08-03 19:24:17][EntityExtractor][180][************************************] INFO: Found policy texts for 4 policy types
2025-08-03 19:24:17,909 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:24:17,909 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:24:17,909 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:17,909 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:17,909 INFO sqlalchemy.engine.Engine [cached since 34.64s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:24:17.861441', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found policy texts for 4 policy types", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:24:17.861433", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:17,909 - sqlalchemy.engine.Engine - INFO - [cached since 34.64s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:24:17.861441', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found policy texts for 4 policy types", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:24:17.861433", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:17,979 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:24:17,979 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:24:18][EntityExtractor][180][************************************] INFO: Processing contact_info with fields: ['support_email']
2025-08-03 19:24:18,299 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:24:18,299 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:24:18,299 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:18,299 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:18,299 INFO sqlalchemy.engine.Engine [cached since 35.03s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:24:18.233354', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Processing contact_info with fields: [\'support_email\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:24:18.233343", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:18,299 - sqlalchemy.engine.Engine - INFO - [cached since 35.03s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:24:18.233354', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Processing contact_info with fields: [\'support_email\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:24:18.233343", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:18,369 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:24:18,369 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 19:24:20,023 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 19:24:20,081 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:24:20,081 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:24:20,081 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:20,081 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:20,082 INFO sqlalchemy.engine.Engine [cached since 36.81s ago] {'analysis_id': 0, 'timestamp': '2025-08-03 19:24:18', 'type': 'openai', 'messages': '[\n    {\n        "role": "user",\n        "content": "\\nYou are an expert at extracting business information from policy documents and website cont ... (10420 characters truncated) ... \\n{\\n    \\"support_email\\": \\"extracted support/customer email or null\\"\\n}\\n\\nReturn only the JSON response, no additional text."\n    }\n]', 'response': '"{\\n    \\"support_email\\": \\"<EMAIL>\\"\\n}"', 'org_id': 'default'}
2025-08-03 19:24:20,082 - sqlalchemy.engine.Engine - INFO - [cached since 36.81s ago] {'analysis_id': 0, 'timestamp': '2025-08-03 19:24:18', 'type': 'openai', 'messages': '[\n    {\n        "role": "user",\n        "content": "\\nYou are an expert at extracting business information from policy documents and website cont ... (10420 characters truncated) ... \\n{\\n    \\"support_email\\": \\"extracted support/customer email or null\\"\\n}\\n\\nReturn only the JSON response, no additional text."\n    }\n]', 'response': '"{\\n    \\"support_email\\": \\"<EMAIL>\\"\\n}"', 'org_id': 'default'}
2025-08-03 19:24:20,155 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:24:20,155 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:24:20][api_response_logger][unknown] INFO: OpenAI API Call - Name: entity_extraction_contact_info, Request ID: None
{
  "response_preview": "{\n    \"support_email\": \"<EMAIL>\"\n}",
  "log_file": "api_logs/openai_20250803_192420_268.json",
  "clean_file": "api_logs/clean_openai_20250803_192420_268.json"
}
[API_LOGGER] OpenAI response logged to: api_logs/openai_20250803_192420_268.json
[API_LOGGER] Clean version at: api_logs/clean_openai_20250803_192420_268.json
[2025-08-03 19:24:20][api_response_logger][unknown] INFO: OpenAI API Call - Name: entity_extraction_contact_info, Request ID: ************************************
{
  "response_preview": "{\n    \"support_email\": \"<EMAIL>\"\n}",
  "log_file": "api_logs/openai_20250803_192420_269_************************************.json",
  "clean_file": "api_logs/clean_openai_20250803_192420_269_************************************.json"
}
[API_LOGGER] OpenAI response logged to: api_logs/openai_20250803_192420_269_************************************.json
[API_LOGGER] Clean version at: api_logs/clean_openai_20250803_192420_269_************************************.json
[2025-08-03 19:24:20][EntityExtractor][180][************************************] INFO: OpenAI contact_info call extracted 1 fields: ['support_email']
2025-08-03 19:24:20,321 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:24:20,321 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:24:20,321 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:20,321 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:20,321 INFO sqlalchemy.engine.Engine [cached since 37.05s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:24:20.270206', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "OpenAI contact_info call extracted 1 fields: [\'support_email\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:24:20.270199", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:20,321 - sqlalchemy.engine.Engine - INFO - [cached since 37.05s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:24:20.270206', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "OpenAI contact_info call extracted 1 fields: [\'support_email\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:24:20.270199", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:20,384 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:24:20,384 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:24:20][EntityExtractor][180][************************************] INFO: Processing shipping_info with fields: ['accepts_international_orders', 'shipping_countries', 'shipping_policy_details']
2025-08-03 19:24:20,554 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:24:20,554 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:24:20,554 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:20,554 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:20,554 INFO sqlalchemy.engine.Engine [cached since 37.28s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:24:20.484228', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Processing shipping_info with fields: [\'accepts_international_orders\', \'shipping_countries\', \'shipping_policy_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:24:20.484220", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:20,554 - sqlalchemy.engine.Engine - INFO - [cached since 37.28s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:24:20.484228', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Processing shipping_info with fields: [\'accepts_international_orders\', \'shipping_countries\', \'shipping_policy_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:24:20.484220", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:20,612 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:24:20,612 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 19:24:21,961 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 19:24:22,020 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:24:22,020 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:24:22,020 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:22,020 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:22,020 INFO sqlalchemy.engine.Engine [cached since 38.75s ago] {'analysis_id': 0, 'timestamp': '2025-08-03 19:24:20', 'type': 'openai', 'messages': '[\n    {\n        "role": "user",\n        "content": "\\nYou are an expert at extracting business information from policy documents and website cont ... (2850 characters truncated) ... \\"shipping_policy_details\\": \\"extracted shipping policy details or null\\"\\n}\\n\\nReturn only the JSON response, no additional text."\n    }\n]', 'response': '"{\\n    \\"accepts_international_orders\\": \\"null\\",\\n    \\"shipping_countries\\": \\"null\\",\\n    \\"shipping_policy_details\\": \\"null\\"\\n}"', 'org_id': 'default'}
2025-08-03 19:24:22,020 - sqlalchemy.engine.Engine - INFO - [cached since 38.75s ago] {'analysis_id': 0, 'timestamp': '2025-08-03 19:24:20', 'type': 'openai', 'messages': '[\n    {\n        "role": "user",\n        "content": "\\nYou are an expert at extracting business information from policy documents and website cont ... (2850 characters truncated) ... \\"shipping_policy_details\\": \\"extracted shipping policy details or null\\"\\n}\\n\\nReturn only the JSON response, no additional text."\n    }\n]', 'response': '"{\\n    \\"accepts_international_orders\\": \\"null\\",\\n    \\"shipping_countries\\": \\"null\\",\\n    \\"shipping_policy_details\\": \\"null\\"\\n}"', 'org_id': 'default'}
2025-08-03 19:24:22,100 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:24:22,100 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:24:22][api_response_logger][unknown] INFO: OpenAI API Call - Name: entity_extraction_shipping_info, Request ID: None
{
  "response_preview": "{\n    \"accepts_international_orders\": \"null\",\n    \"shipping_countries\": \"null\",\n    \"shipping_policy_details\": \"null\"\n}",
  "log_file": "api_logs/openai_20250803_192422_206.json",
  "clean_file": "api_logs/clean_openai_20250803_192422_206.json"
}
[API_LOGGER] OpenAI response logged to: api_logs/openai_20250803_192422_206.json
[API_LOGGER] Clean version at: api_logs/clean_openai_20250803_192422_206.json
[2025-08-03 19:24:22][api_response_logger][unknown] INFO: OpenAI API Call - Name: entity_extraction_shipping_info, Request ID: ************************************
{
  "response_preview": "{\n    \"accepts_international_orders\": \"null\",\n    \"shipping_countries\": \"null\",\n    \"shipping_policy_details\": \"null\"\n}",
  "log_file": "api_logs/openai_20250803_192422_206_************************************.json",
  "clean_file": "api_logs/clean_openai_20250803_192422_206_************************************.json"
}
[API_LOGGER] OpenAI response logged to: api_logs/openai_20250803_192422_206_************************************.json
[API_LOGGER] Clean version at: api_logs/clean_openai_20250803_192422_206_************************************.json
[2025-08-03 19:24:22][EntityExtractor][180][************************************] INFO: OpenAI shipping_info call extracted 0 fields: []
2025-08-03 19:24:22,261 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:24:22,261 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:24:22,261 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:22,261 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:22,261 INFO sqlalchemy.engine.Engine [cached since 38.99s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:24:22.207143', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "OpenAI shipping_info call extracted 0 fields: []", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:24:22.207138", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:22,261 - sqlalchemy.engine.Engine - INFO - [cached since 38.99s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:24:22.207143', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "OpenAI shipping_info call extracted 0 fields: []", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:24:22.207138", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:22,322 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:24:22,322 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:24:22][EntityExtractor][180][************************************] INFO: Making final comprehensive call for remaining fields: ['accepts_international_orders', 'shipping_countries', 'shipping_policy_details']
2025-08-03 19:24:22,481 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:24:22,481 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:24:22,481 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:22,481 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:22,481 INFO sqlalchemy.engine.Engine [cached since 39.21s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:24:22.430060', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Making final comprehensive call for remaining fields: [\'accepts_international_orders\', \'shipping_countries\', \'shipping_policy_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:24:22.430053", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:22,481 - sqlalchemy.engine.Engine - INFO - [cached since 39.21s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:24:22.430060', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Making final comprehensive call for remaining fields: [\'accepts_international_orders\', \'shipping_countries\', \'shipping_policy_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:24:22.430053", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:22,541 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:24:22,541 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 19:24:24,001 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 19:24:24,071 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:24:24,071 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:24:24,071 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:24,071 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:24,071 INFO sqlalchemy.engine.Engine [cached since 40.8s ago] {'analysis_id': 0, 'timestamp': '2025-08-03 19:24:22', 'type': 'openai', 'messages': '[\n    {\n        "role": "user",\n        "content": "\\nYou are an expert at extracting business information from policy documents and website cont ... (14567 characters truncated) ... \\"shipping_policy_details\\": \\"extracted shipping policy details or null\\"\\n}\\n\\nReturn only the JSON response, no additional text."\n    }\n]', 'response': '"{\\n    \\"accepts_international_orders\\": \\"no\\",\\n    \\"shipping_countries\\": \\"null\\",\\n    \\"shipping_policy_details\\": \\"null\\"\\n}"', 'org_id': 'default'}
2025-08-03 19:24:24,071 - sqlalchemy.engine.Engine - INFO - [cached since 40.8s ago] {'analysis_id': 0, 'timestamp': '2025-08-03 19:24:22', 'type': 'openai', 'messages': '[\n    {\n        "role": "user",\n        "content": "\\nYou are an expert at extracting business information from policy documents and website cont ... (14567 characters truncated) ... \\"shipping_policy_details\\": \\"extracted shipping policy details or null\\"\\n}\\n\\nReturn only the JSON response, no additional text."\n    }\n]', 'response': '"{\\n    \\"accepts_international_orders\\": \\"no\\",\\n    \\"shipping_countries\\": \\"null\\",\\n    \\"shipping_policy_details\\": \\"null\\"\\n}"', 'org_id': 'default'}
2025-08-03 19:24:24,212 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:24:24,212 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:24:24][api_response_logger][unknown] INFO: OpenAI API Call - Name: entity_extraction_comprehensive, Request ID: None
{
  "response_preview": "{\n    \"accepts_international_orders\": \"no\",\n    \"shipping_countries\": \"null\",\n    \"shipping_policy_details\": \"null\"\n}",
  "log_file": "api_logs/openai_20250803_192424_316.json",
  "clean_file": "api_logs/clean_openai_20250803_192424_316.json"
}
[API_LOGGER] OpenAI response logged to: api_logs/openai_20250803_192424_316.json
[API_LOGGER] Clean version at: api_logs/clean_openai_20250803_192424_316.json
[2025-08-03 19:24:24][api_response_logger][unknown] INFO: OpenAI API Call - Name: entity_extraction_comprehensive, Request ID: ************************************
{
  "response_preview": "{\n    \"accepts_international_orders\": \"no\",\n    \"shipping_countries\": \"null\",\n    \"shipping_policy_details\": \"null\"\n}",
  "log_file": "api_logs/openai_20250803_192424_316_************************************.json",
  "clean_file": "api_logs/clean_openai_20250803_192424_316_************************************.json"
}
[API_LOGGER] OpenAI response logged to: api_logs/openai_20250803_192424_316_************************************.json
[API_LOGGER] Clean version at: api_logs/clean_openai_20250803_192424_316_************************************.json
[2025-08-03 19:24:24][EntityExtractor][180][************************************] INFO: OpenAI comprehensive call extracted 1 fields: ['accepts_international_orders']
2025-08-03 19:24:24,388 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:24:24,388 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:24:24,388 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:24,388 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:24,388 INFO sqlalchemy.engine.Engine [cached since 41.11s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:24:24.317194', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "OpenAI comprehensive call extracted 1 fields: [\'accepts_international_orders\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:24:24.317190", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:24,388 - sqlalchemy.engine.Engine - INFO - [cached since 41.11s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:24:24.317194', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "OpenAI comprehensive call extracted 1 fields: [\'accepts_international_orders\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:24:24.317190", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:24,434 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:24:24,434 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:24:24][EntityExtractor][180][************************************] INFO: Total OpenAI backup extracted 2 fields: ['support_email', 'accepts_international_orders']
2025-08-03 19:24:24,594 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:24:24,594 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:24:24,595 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:24,595 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:24,595 INFO sqlalchemy.engine.Engine [cached since 41.32s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:24:24.541576', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Total OpenAI backup extracted 2 fields: [\'support_email\', \'accepts_international_orders\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:24:24.541566", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:24,595 - sqlalchemy.engine.Engine - INFO - [cached since 41.32s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:24:24.541576', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Total OpenAI backup extracted 2 fields: [\'support_email\', \'accepts_international_orders\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:24:24.541566", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:24,655 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:24:24,655 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:24:24][EntityExtractor][180][************************************] INFO: OpenAI backup extracted: ['support_email', 'accepts_international_orders']
2025-08-03 19:24:24,811 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:24:24,811 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:24:24,811 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:24,811 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:24,811 INFO sqlalchemy.engine.Engine [cached since 41.54s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:24:24.761527', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "OpenAI backup extracted: [\'support_email\', \'accepts_international_orders\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:24:24.761520", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:24,811 - sqlalchemy.engine.Engine - INFO - [cached since 41.54s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:24:24.761527', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "OpenAI backup extracted: [\'support_email\', \'accepts_international_orders\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:24:24.761520", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:24,871 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:24:24,871 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:24:24][EntityExtractor][180][************************************] INFO: Merging 2 AI extraction results
2025-08-03 19:24:25,032 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:24:25,032 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:24:25,032 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:25,032 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:25,032 INFO sqlalchemy.engine.Engine [cached since 41.76s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:24:24.981446', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Merging 2 AI extraction results", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:24:24.981439", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:25,032 - sqlalchemy.engine.Engine - INFO - [cached since 41.76s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:24:24.981446', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Merging 2 AI extraction results", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:24:24.981439", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:25,091 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:24:25,091 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:24:25][EntityExtractor][180][************************************] INFO: Merged result contains 9 fields
2025-08-03 19:24:25,269 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:24:25,269 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:24:25,269 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:25,269 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:25,269 INFO sqlalchemy.engine.Engine [cached since 42s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:24:25.210278', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Merged result contains 9 fields", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:24:25.210271", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:25,269 - sqlalchemy.engine.Engine - INFO - [cached since 42s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:24:25.210278', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Merged result contains 9 fields", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:24:25.210271", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:25,330 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:24:25,330 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:24:25][EntityExtractor][180][************************************] INFO: Final merged extraction result keys: ['legal_name', 'business_email', 'business_contact_numbers', 'business_location', 'accepts_international_orders', 'has_jurisdiction_law', 'jurisdiction_place', 'jurisdiction_details', 'support_email']
2025-08-03 19:24:25,488 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:24:25,488 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:24:25,488 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:25,488 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:25,488 INFO sqlalchemy.engine.Engine [cached since 42.21s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:24:25.432210', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Final merged extraction result keys: [\'legal_name\', \'business_email\', \'business_contact_numbers\', \'business_loca ... (109 characters truncated) ... tails\', \'support_email\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:24:25.432203", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:25,488 - sqlalchemy.engine.Engine - INFO - [cached since 42.21s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:24:25.432210', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Final merged extraction result keys: [\'legal_name\', \'business_email\', \'business_contact_numbers\', \'business_loca ... (109 characters truncated) ... tails\', \'support_email\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:24:25.432203", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:25,559 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:24:25,559 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:24:25][EntityExtractor][180][************************************] INFO: Fallback raw text for privacy_policy_text: present
2025-08-03 19:24:25,721 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:24:25,721 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:24:25,721 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:25,721 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:25,721 INFO sqlalchemy.engine.Engine [cached since 42.45s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:24:25.669475', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Fallback raw text for privacy_policy_text: present", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:24:25.669469", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:25,721 - sqlalchemy.engine.Engine - INFO - [cached since 42.45s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:24:25.669475', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Fallback raw text for privacy_policy_text: present", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:24:25.669469", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:25,781 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:24:25,781 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:24:25][EntityExtractor][180][************************************] INFO: After fallback, merged_result[privacy_policy_text] length: 2340
2025-08-03 19:24:25,932 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:24:25,932 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:24:25,932 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:25,932 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:25,932 INFO sqlalchemy.engine.Engine [cached since 42.66s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:24:25.881534', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "After fallback, merged_result[privacy_policy_text] length: 2340", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:24:25.881526", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:25,932 - sqlalchemy.engine.Engine - INFO - [cached since 42.66s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:24:25.881534', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "After fallback, merged_result[privacy_policy_text] length: 2340", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:24:25.881526", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:25,991 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:24:25,991 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:24:26][EntityExtractor][180][************************************] INFO: Fallback raw text for terms_conditions_text: present
2025-08-03 19:24:26,153 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:24:26,153 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:24:26,153 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:26,153 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:26,153 INFO sqlalchemy.engine.Engine [cached since 42.88s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:24:26.096421', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Fallback raw text for terms_conditions_text: present", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:24:26.096412", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:26,153 - sqlalchemy.engine.Engine - INFO - [cached since 42.88s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:24:26.096421', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Fallback raw text for terms_conditions_text: present", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:24:26.096412", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:26,219 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:24:26,219 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:24:26][EntityExtractor][180][************************************] INFO: After fallback, merged_result[terms_conditions_text] length: 1479
2025-08-03 19:24:26,379 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:24:26,379 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:24:26,379 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:26,379 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:26,379 INFO sqlalchemy.engine.Engine [cached since 43.11s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:24:26.320814', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "After fallback, merged_result[terms_conditions_text] length: 1479", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:24:26.320806", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:26,379 - sqlalchemy.engine.Engine - INFO - [cached since 43.11s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:24:26.320814', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "After fallback, merged_result[terms_conditions_text] length: 1479", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:24:26.320806", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:26,427 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:24:26,427 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 19:24:26,581 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:24:26,581 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:24:26,581 INFO sqlalchemy.engine.Engine SELECT entity_extraction_analysis.id AS entity_extraction_analysis_id, entity_extraction_analysis.scrape_request_ref_id AS entity_extraction_analysis_scrape_request_ref_id, entity_extraction_analysis.website_url AS entity_extraction_analysis_website_url, entity_extraction_analysis.processing_status AS entity_extraction_analysis_processing_status, entity_extraction_analysis.legal_name AS entity_extraction_analysis_legal_name, entity_extraction_analysis.business_email AS entity_extraction_analysis_business_email, entity_extraction_analysis.support_email AS entity_extraction_analysis_support_email, entity_extraction_analysis.business_contact_numbers AS entity_extraction_analysis_business_contact_numbers, entity_extraction_analysis.business_location AS entity_extraction_analysis_business_location, entity_extraction_analysis.has_jurisdiction_law AS entity_extraction_analysis_has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details AS entity_extraction_analysis_jurisdiction_details, entity_extraction_analysis.accepts_international_orders AS entity_extraction_analysis_accepts_international_orders, entity_extraction_analysis.shipping_policy_details AS entity_extraction_analysis_shipping_policy_details, entity_extraction_analysis.jurisdiction_place AS entity_extraction_analysis_jurisdiction_place, entity_extraction_analysis.shipping_countries AS entity_extraction_analysis_shipping_countries, entity_extraction_analysis.privacy_policy_text AS entity_extraction_analysis_privacy_policy_text, entity_extraction_analysis.terms_conditions_text AS entity_extraction_analysis_terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini AS entity_extraction_analysis_urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini AS entity_extraction_analysis_urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method AS entity_extraction_analysis_extraction_method, entity_extraction_analysis.total_urls_processed AS entity_extraction_analysis_total_urls_processed, entity_extraction_analysis.all_urls_found AS entity_extraction_analysis_all_urls_found, entity_extraction_analysis.reachable_urls AS entity_extraction_analysis_reachable_urls, entity_extraction_analysis.unreachable_urls AS entity_extraction_analysis_unreachable_urls, entity_extraction_analysis.policy_urls_matched AS entity_extraction_analysis_policy_urls_matched, entity_extraction_analysis.created_at AS entity_extraction_analysis_created_at, entity_extraction_analysis.started_at AS entity_extraction_analysis_started_at, entity_extraction_analysis.completed_at AS entity_extraction_analysis_completed_at, entity_extraction_analysis.error_message AS entity_extraction_analysis_error_message, entity_extraction_analysis.org_id AS entity_extraction_analysis_org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.id = %(pk_1)s
2025-08-03 19:24:26,581 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_analysis.id AS entity_extraction_analysis_id, entity_extraction_analysis.scrape_request_ref_id AS entity_extraction_analysis_scrape_request_ref_id, entity_extraction_analysis.website_url AS entity_extraction_analysis_website_url, entity_extraction_analysis.processing_status AS entity_extraction_analysis_processing_status, entity_extraction_analysis.legal_name AS entity_extraction_analysis_legal_name, entity_extraction_analysis.business_email AS entity_extraction_analysis_business_email, entity_extraction_analysis.support_email AS entity_extraction_analysis_support_email, entity_extraction_analysis.business_contact_numbers AS entity_extraction_analysis_business_contact_numbers, entity_extraction_analysis.business_location AS entity_extraction_analysis_business_location, entity_extraction_analysis.has_jurisdiction_law AS entity_extraction_analysis_has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details AS entity_extraction_analysis_jurisdiction_details, entity_extraction_analysis.accepts_international_orders AS entity_extraction_analysis_accepts_international_orders, entity_extraction_analysis.shipping_policy_details AS entity_extraction_analysis_shipping_policy_details, entity_extraction_analysis.jurisdiction_place AS entity_extraction_analysis_jurisdiction_place, entity_extraction_analysis.shipping_countries AS entity_extraction_analysis_shipping_countries, entity_extraction_analysis.privacy_policy_text AS entity_extraction_analysis_privacy_policy_text, entity_extraction_analysis.terms_conditions_text AS entity_extraction_analysis_terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini AS entity_extraction_analysis_urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini AS entity_extraction_analysis_urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method AS entity_extraction_analysis_extraction_method, entity_extraction_analysis.total_urls_processed AS entity_extraction_analysis_total_urls_processed, entity_extraction_analysis.all_urls_found AS entity_extraction_analysis_all_urls_found, entity_extraction_analysis.reachable_urls AS entity_extraction_analysis_reachable_urls, entity_extraction_analysis.unreachable_urls AS entity_extraction_analysis_unreachable_urls, entity_extraction_analysis.policy_urls_matched AS entity_extraction_analysis_policy_urls_matched, entity_extraction_analysis.created_at AS entity_extraction_analysis_created_at, entity_extraction_analysis.started_at AS entity_extraction_analysis_started_at, entity_extraction_analysis.completed_at AS entity_extraction_analysis_completed_at, entity_extraction_analysis.error_message AS entity_extraction_analysis_error_message, entity_extraction_analysis.org_id AS entity_extraction_analysis_org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.id = %(pk_1)s
2025-08-03 19:24:26,582 INFO sqlalchemy.engine.Engine [cached since 41.14s ago] {'pk_1': 180}
2025-08-03 19:24:26,582 - sqlalchemy.engine.Engine - INFO - [cached since 41.14s ago] {'pk_1': 180}
2025-08-03 19:24:26,663 INFO sqlalchemy.engine.Engine UPDATE entity_extraction_analysis SET processing_status=%(processing_status)s, business_location=%(business_location)s, jurisdiction_details=%(jurisdiction_details)s, accepts_international_orders=%(accepts_international_orders)s, jurisdiction_place=%(jurisdiction_place)s, completed_at=%(completed_at)s WHERE entity_extraction_analysis.id = %(entity_extraction_analysis_id)s
2025-08-03 19:24:26,663 - sqlalchemy.engine.Engine - INFO - UPDATE entity_extraction_analysis SET processing_status=%(processing_status)s, business_location=%(business_location)s, jurisdiction_details=%(jurisdiction_details)s, accepts_international_orders=%(accepts_international_orders)s, jurisdiction_place=%(jurisdiction_place)s, completed_at=%(completed_at)s WHERE entity_extraction_analysis.id = %(entity_extraction_analysis_id)s
2025-08-03 19:24:26,663 INFO sqlalchemy.engine.Engine [generated in 0.00018s] {'processing_status': 'COMPLETED', 'business_location': 'Commerzone, Block-2, No.2, 200 Feet Radial Road, Pallikaranai, Chennai – 600100 India', 'jurisdiction_details': 'Website owned and operated by an Indian company, implying Indian jurisdiction. [2]', 'accepts_international_orders': False, 'jurisdiction_place': 'India', 'completed_at': '2025-08-03T19:24:26.662689', 'entity_extraction_analysis_id': 180}
2025-08-03 19:24:26,663 - sqlalchemy.engine.Engine - INFO - [generated in 0.00018s] {'processing_status': 'COMPLETED', 'business_location': 'Commerzone, Block-2, No.2, 200 Feet Radial Road, Pallikaranai, Chennai – 600100 India', 'jurisdiction_details': 'Website owned and operated by an Indian company, implying Indian jurisdiction. [2]', 'accepts_international_orders': False, 'jurisdiction_place': 'India', 'completed_at': '2025-08-03T19:24:26.662689', 'entity_extraction_analysis_id': 180}
2025-08-03 19:24:26,737 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:24:26,737 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:24:26][EntityExtractor][180][************************************] INFO: Updated analysis 180 with merged results and completed_at timestamp
2025-08-03 19:24:26,902 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:24:26,902 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:24:26,902 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:26,902 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:26,902 INFO sqlalchemy.engine.Engine [cached since 43.63s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:24:26.841139', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Updated analysis 180 with merged results and completed_at timestamp", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:24:26.841132", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:26,902 - sqlalchemy.engine.Engine - INFO - [cached since 43.63s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:24:26.841139', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Updated analysis 180 with merged results and completed_at timestamp", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:24:26.841132", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:26,980 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:24:26,980 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:24:27][EntityExtractor][180][************************************] INFO: Creating response with merged_result keys: ['legal_name', 'business_email', 'business_contact_numbers', 'business_location', 'accepts_international_orders', 'has_jurisdiction_law', 'jurisdiction_place', 'jurisdiction_details', 'support_email', 'privacy_policy_text', 'terms_conditions_text']
2025-08-03 19:24:27,255 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:24:27,255 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:24:27,255 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:27,255 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:27,255 INFO sqlalchemy.engine.Engine [cached since 43.98s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:24:27.157800', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Creating response with merged_result keys: [\'legal_name\', \'business_email\', \'business_contact_numbers\', \'busines ... (167 characters truncated) ...  \'terms_conditions_text\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:24:27.157793", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:27,255 - sqlalchemy.engine.Engine - INFO - [cached since 43.98s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:24:27.157800', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Creating response with merged_result keys: [\'legal_name\', \'business_email\', \'business_contact_numbers\', \'busines ... (167 characters truncated) ...  \'terms_conditions_text\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:24:27.157793", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:27,322 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:24:27,322 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:24:27][EntityExtractor][180][************************************] INFO: Response jurisdiction_details: Website owned and operated by an Indian company, implying Indian jurisdiction. [2]
2025-08-03 19:24:27,472 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:24:27,472 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:24:27,472 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:27,472 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:27,472 INFO sqlalchemy.engine.Engine [cached since 44.2s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:24:27.422163', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Response jurisdiction_details: Website owned and operated by an Indian company, implying Indian jurisdiction. [2]", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:24:27.422156", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:27,472 - sqlalchemy.engine.Engine - INFO - [cached since 44.2s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:24:27.422163', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Response jurisdiction_details: Website owned and operated by an Indian company, implying Indian jurisdiction. [2]", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:24:27.422156", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:27,531 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:24:27,531 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:24:27][EntityExtractor][180][************************************] INFO: Response jurisdiction_place: ['India']
2025-08-03 19:24:27,693 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:24:27,693 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:24:27,693 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:27,693 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:27,693 INFO sqlalchemy.engine.Engine [cached since 44.42s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:24:27.631270', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Response jurisdiction_place: [\'India\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:24:27.631262", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:27,693 - sqlalchemy.engine.Engine - INFO - [cached since 44.42s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:24:27.631270', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Response jurisdiction_place: [\'India\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:24:27.631262", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:27,752 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:24:27,752 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:24:27][EntityExtractor][180][************************************] INFO: Response has_jurisdiction_law: yes
2025-08-03 19:24:27,902 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:24:27,902 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:24:27,902 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:27,902 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:27,902 INFO sqlalchemy.engine.Engine [cached since 44.63s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:24:27.851555', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Response has_jurisdiction_law: yes", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:24:27.851547", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:27,902 - sqlalchemy.engine.Engine - INFO - [cached since 44.63s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:24:27.851555', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Response has_jurisdiction_law: yes", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:24:27.851547", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:27,971 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:24:27,971 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:24:28][EntityExtractor][background_processor][background] INFO: Entity extraction completed for https://www.shell.in
2025-08-03 19:24:28,126 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:24:28,126 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:24:28,126 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:28,126 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:28,126 INFO sqlalchemy.engine.Engine [cached since 44.85s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:24:28.070189', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Entity extraction completed for https://www.shell.in", "scrape_request_ref_id": "background", "timestamp": "2025-08-03T19:24:28.070184", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:28,126 - sqlalchemy.engine.Engine - INFO - [cached since 44.85s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:24:28.070189', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Entity extraction completed for https://www.shell.in", "scrape_request_ref_id": "background", "timestamp": "2025-08-03T19:24:28.070184", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:28,192 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:24:28,192 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:24:28][EntityExtractor][background_processor][background] INFO: Sending extraction results to Java server (sync) for https://www.shell.in
2025-08-03 19:24:28,352 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:24:28,352 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:24:28,353 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:28,353 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:28,353 INFO sqlalchemy.engine.Engine [cached since 45.08s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:24:28.294195', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Sending extraction results to Java server (sync) for https://www.shell.in", "scrape_request_ref_id": "background", "timestamp": "2025-08-03T19:24:28.294189", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:28,353 - sqlalchemy.engine.Engine - INFO - [cached since 45.08s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:24:28.294195', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Sending extraction results to Java server (sync) for https://www.shell.in", "scrape_request_ref_id": "background", "timestamp": "2025-08-03T19:24:28.294189", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:28,426 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:24:28,426 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:24:28][EntityExtractor][background_processor][background] INFO: Sending PATCH request to: http://localhost:8080/api/entity-extraction/results
2025-08-03 19:24:28,582 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:24:28,582 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:24:28,582 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:28,582 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:28,582 INFO sqlalchemy.engine.Engine [cached since 45.31s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:24:28.531423', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Sending PATCH request to: http://localhost:8080/api/entity-extraction/results", "scrape_request_ref_id": "background", "timestamp": "2025-08-03T19:24:28.531418", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:28,582 - sqlalchemy.engine.Engine - INFO - [cached since 45.31s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:24:28.531423', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Sending PATCH request to: http://localhost:8080/api/entity-extraction/results", "scrape_request_ref_id": "background", "timestamp": "2025-08-03T19:24:28.531418", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:28,649 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:24:28,649 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:24:28][EntityExtractor][background_processor][background] DEBUG: Payload org_id type: <class 'int'>, value: 2
2025-08-03 19:24:28,812 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:24:28,812 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:24:28,812 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:28,812 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:28,812 INFO sqlalchemy.engine.Engine [cached since 45.54s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:24:28.761547', 'type': 'entity_extractor', 'messages': '{"level": "DEBUG", "message": "Payload org_id type: <class \'int\'>, value: 2", "scrape_request_ref_id": "background", "timestamp": "2025-08-03T19:24:28.761539", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:28,812 - sqlalchemy.engine.Engine - INFO - [cached since 45.54s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:24:28.761547', 'type': 'entity_extractor', 'messages': '{"level": "DEBUG", "message": "Payload org_id type: <class \'int\'>, value: 2", "scrape_request_ref_id": "background", "timestamp": "2025-08-03T19:24:28.761539", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:28,873 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:24:28,873 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:24:28][EntityExtractor][background_processor][background] ERROR: Request error sending results to Java server: [Errno 111] Connection refused
2025-08-03 19:24:29,047 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:24:29,047 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:24:29,047 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:29,047 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:29,047 INFO sqlalchemy.engine.Engine [cached since 45.77s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:24:28.980719', 'type': 'entity_extractor', 'messages': '{"level": "ERROR", "message": "Request error sending results to Java server: [Errno 111] Connection refused", "scrape_request_ref_id": "background", "timestamp": "2025-08-03T19:24:28.980713", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:29,047 - sqlalchemy.engine.Engine - INFO - [cached since 45.77s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:24:28.980719', 'type': 'entity_extractor', 'messages': '{"level": "ERROR", "message": "Request error sending results to Java server: [Errno 111] Connection refused", "scrape_request_ref_id": "background", "timestamp": "2025-08-03T19:24:28.980713", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:29,120 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:24:29,120 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:24:29][EntityExtractor][background_processor][background] ERROR: Failed to send results to Java server: Request error sending results to Java server: [Errno 111] Connection refused
2025-08-03 19:24:29,288 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:24:29,288 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:24:29,289 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:29,289 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:24:29,289 INFO sqlalchemy.engine.Engine [cached since 46.01s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:24:29.228806', 'type': 'entity_extractor', 'messages': '{"level": "ERROR", "message": "Failed to send results to Java server: Request error sending results to Java server: [Errno 111] Connection refused", "scrape_request_ref_id": "background", "timestamp": "2025-08-03T19:24:29.228798", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:29,289 - sqlalchemy.engine.Engine - INFO - [cached since 46.01s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:24:29.228806', 'type': 'entity_extractor', 'messages': '{"level": "ERROR", "message": "Failed to send results to Java server: Request error sending results to Java server: [Errno 111] Connection refused", "scrape_request_ref_id": "background", "timestamp": "2025-08-03T19:24:29.228798", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:24:29,354 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:24:29,354 - sqlalchemy.engine.Engine - INFO - COMMIT
INFO:     127.0.0.1:53382 - "POST /entity-extraction/analyze HTTP/1.1" 200 OK
[2025-08-03 19:32:18][EntityExtractor][background_processor][background] INFO: Java server client initialized with base URL: http://localhost:8080
2025-08-03 19:32:18,170 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:32:18,170 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:32:18,170 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:18,170 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:18,170 INFO sqlalchemy.engine.Engine [cached since 514.9s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:32:18.123506', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Java server client initialized with base URL: http://localhost:8080", "scrape_request_ref_id": "background", "timestamp": "2025-08-03T19:32:18.123499", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:32:18,170 - sqlalchemy.engine.Engine - INFO - [cached since 514.9s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:32:18.123506', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Java server client initialized with base URL: http://localhost:8080", "scrape_request_ref_id": "background", "timestamp": "2025-08-03T19:32:18.123499", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:32:18,231 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:32:18,231 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:32:18][EntityExtractor][background_processor][background] INFO: Starting background processing for https://www.shell.in
2025-08-03 19:32:18,415 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:32:18,415 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:32:18,416 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:18,416 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:18,416 INFO sqlalchemy.engine.Engine [cached since 515.1s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:32:18.331486', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Starting background processing for https://www.shell.in", "scrape_request_ref_id": "background", "timestamp": "2025-08-03T19:32:18.331473", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:32:18,416 - sqlalchemy.engine.Engine - INFO - [cached since 515.1s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:32:18.331486', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Starting background processing for https://www.shell.in", "scrape_request_ref_id": "background", "timestamp": "2025-08-03T19:32:18.331473", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:32:18,487 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:32:18,487 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:32:18][EntityExtractor][orchestrator_************************************][************************************] INFO: Starting simplified entity extraction orchestration
{
  "scrape_request_ref_id": "************************************",
  "website_url": "https://www.shell.in",
  "org_id": 2
}
2025-08-03 19:32:18,723 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:32:18,723 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:32:18,723 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:18,723 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:18,723 INFO sqlalchemy.engine.Engine [cached since 515.4s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:32:18.674104', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Starting simplified entity extraction orchestration", "scrape_request_ref_id": "************************************",  ... (20 characters truncated) ... 8-03T19:32:18.674095", "data": {"scrape_request_ref_id": "************************************", "website_url": "https://www.shell.in", "org_id": 2}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:32:18,723 - sqlalchemy.engine.Engine - INFO - [cached since 515.4s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:32:18.674104', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Starting simplified entity extraction orchestration", "scrape_request_ref_id": "************************************",  ... (20 characters truncated) ... 8-03T19:32:18.674095", "data": {"scrape_request_ref_id": "************************************", "website_url": "https://www.shell.in", "org_id": 2}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:32:18,782 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:32:18,782 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 19:32:18,938 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:32:18,938 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:32:18,938 INFO sqlalchemy.engine.Engine SELECT entity_extraction_analysis.id, entity_extraction_analysis.scrape_request_ref_id, entity_extraction_analysis.website_url, entity_extraction_analysis.processing_status, entity_extraction_analysis.legal_name, entity_extraction_analysis.business_email, entity_extraction_analysis.support_email, entity_extraction_analysis.business_contact_numbers, entity_extraction_analysis.business_location, entity_extraction_analysis.has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details, entity_extraction_analysis.accepts_international_orders, entity_extraction_analysis.shipping_policy_details, entity_extraction_analysis.jurisdiction_place, entity_extraction_analysis.shipping_countries, entity_extraction_analysis.privacy_policy_text, entity_extraction_analysis.terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method, entity_extraction_analysis.total_urls_processed, entity_extraction_analysis.all_urls_found, entity_extraction_analysis.reachable_urls, entity_extraction_analysis.unreachable_urls, entity_extraction_analysis.policy_urls_matched, entity_extraction_analysis.created_at, entity_extraction_analysis.started_at, entity_extraction_analysis.completed_at, entity_extraction_analysis.error_message, entity_extraction_analysis.org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.scrape_request_ref_id = %(scrape_request_ref_id_1)s AND entity_extraction_analysis.org_id = %(org_id_1)s ORDER BY entity_extraction_analysis.id DESC
2025-08-03 19:32:18,938 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_analysis.id, entity_extraction_analysis.scrape_request_ref_id, entity_extraction_analysis.website_url, entity_extraction_analysis.processing_status, entity_extraction_analysis.legal_name, entity_extraction_analysis.business_email, entity_extraction_analysis.support_email, entity_extraction_analysis.business_contact_numbers, entity_extraction_analysis.business_location, entity_extraction_analysis.has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details, entity_extraction_analysis.accepts_international_orders, entity_extraction_analysis.shipping_policy_details, entity_extraction_analysis.jurisdiction_place, entity_extraction_analysis.shipping_countries, entity_extraction_analysis.privacy_policy_text, entity_extraction_analysis.terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method, entity_extraction_analysis.total_urls_processed, entity_extraction_analysis.all_urls_found, entity_extraction_analysis.reachable_urls, entity_extraction_analysis.unreachable_urls, entity_extraction_analysis.policy_urls_matched, entity_extraction_analysis.created_at, entity_extraction_analysis.started_at, entity_extraction_analysis.completed_at, entity_extraction_analysis.error_message, entity_extraction_analysis.org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.scrape_request_ref_id = %(scrape_request_ref_id_1)s AND entity_extraction_analysis.org_id = %(org_id_1)s ORDER BY entity_extraction_analysis.id DESC
2025-08-03 19:32:18,938 INFO sqlalchemy.engine.Engine [cached since 515s ago] {'scrape_request_ref_id_1': '************************************', 'org_id_1': 2}
2025-08-03 19:32:18,938 - sqlalchemy.engine.Engine - INFO - [cached since 515s ago] {'scrape_request_ref_id_1': '************************************', 'org_id_1': 2}
[2025-08-03 19:32:19][EntityExtractor][orchestrator_************************************][************************************] INFO: Found existing analysis with ID: 180, status: COMPLETED
2025-08-03 19:32:19,092 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:32:19,092 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:32:19,092 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:19,092 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:19,092 INFO sqlalchemy.engine.Engine [cached since 515.8s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:32:19.018677', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found existing analysis with ID: 180, status: COMPLETED", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:32:19.018669", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:32:19,092 - sqlalchemy.engine.Engine - INFO - [cached since 515.8s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:32:19.018677', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found existing analysis with ID: 180, status: COMPLETED", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:32:19.018669", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:32:19,151 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:32:19,151 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 19:32:19,253 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-03 19:32:19,253 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-03 19:32:19,400 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:32:19,400 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:32:19,400 INFO sqlalchemy.engine.Engine SELECT entity_extraction_analysis.id, entity_extraction_analysis.scrape_request_ref_id, entity_extraction_analysis.website_url, entity_extraction_analysis.processing_status, entity_extraction_analysis.legal_name, entity_extraction_analysis.business_email, entity_extraction_analysis.support_email, entity_extraction_analysis.business_contact_numbers, entity_extraction_analysis.business_location, entity_extraction_analysis.has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details, entity_extraction_analysis.accepts_international_orders, entity_extraction_analysis.shipping_policy_details, entity_extraction_analysis.jurisdiction_place, entity_extraction_analysis.shipping_countries, entity_extraction_analysis.privacy_policy_text, entity_extraction_analysis.terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method, entity_extraction_analysis.total_urls_processed, entity_extraction_analysis.all_urls_found, entity_extraction_analysis.reachable_urls, entity_extraction_analysis.unreachable_urls, entity_extraction_analysis.policy_urls_matched, entity_extraction_analysis.created_at, entity_extraction_analysis.started_at, entity_extraction_analysis.completed_at, entity_extraction_analysis.error_message, entity_extraction_analysis.org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.scrape_request_ref_id = %(scrape_request_ref_id_1)s AND entity_extraction_analysis.org_id = %(org_id_1)s ORDER BY entity_extraction_analysis.id DESC
2025-08-03 19:32:19,400 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_analysis.id, entity_extraction_analysis.scrape_request_ref_id, entity_extraction_analysis.website_url, entity_extraction_analysis.processing_status, entity_extraction_analysis.legal_name, entity_extraction_analysis.business_email, entity_extraction_analysis.support_email, entity_extraction_analysis.business_contact_numbers, entity_extraction_analysis.business_location, entity_extraction_analysis.has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details, entity_extraction_analysis.accepts_international_orders, entity_extraction_analysis.shipping_policy_details, entity_extraction_analysis.jurisdiction_place, entity_extraction_analysis.shipping_countries, entity_extraction_analysis.privacy_policy_text, entity_extraction_analysis.terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method, entity_extraction_analysis.total_urls_processed, entity_extraction_analysis.all_urls_found, entity_extraction_analysis.reachable_urls, entity_extraction_analysis.unreachable_urls, entity_extraction_analysis.policy_urls_matched, entity_extraction_analysis.created_at, entity_extraction_analysis.started_at, entity_extraction_analysis.completed_at, entity_extraction_analysis.error_message, entity_extraction_analysis.org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.scrape_request_ref_id = %(scrape_request_ref_id_1)s AND entity_extraction_analysis.org_id = %(org_id_1)s ORDER BY entity_extraction_analysis.id DESC
2025-08-03 19:32:19,400 INFO sqlalchemy.engine.Engine [cached since 515.4s ago] {'scrape_request_ref_id_1': '************************************', 'org_id_1': 2}
2025-08-03 19:32:19,400 - sqlalchemy.engine.Engine - INFO - [cached since 515.4s ago] {'scrape_request_ref_id_1': '************************************', 'org_id_1': 2}
[2025-08-03 19:32:19][EntityExtractor][orchestrator_************************************][************************************] WARNING: Found existing analysis during create - returning existing ID: 180
2025-08-03 19:32:19,530 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:32:19,530 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:32:19,530 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:19,530 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:19,530 INFO sqlalchemy.engine.Engine [cached since 516.3s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:32:19.470749', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "Found existing analysis during create - returning existing ID: 180", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:32:19.470741", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:32:19,530 - sqlalchemy.engine.Engine - INFO - [cached since 516.3s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:32:19.470749', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "Found existing analysis during create - returning existing ID: 180", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:32:19.470741", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:32:19,590 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:32:19,590 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 19:32:19,717 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-03 19:32:19,717 - sqlalchemy.engine.Engine - INFO - ROLLBACK
[2025-08-03 19:32:19][EntityExtractor][180][************************************] INFO: Updated logger with analysis ID: 180
2025-08-03 19:32:19,889 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:32:19,889 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:32:19,889 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:19,889 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:19,889 INFO sqlalchemy.engine.Engine [cached since 516.6s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:32:19.830552', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Updated logger with analysis ID: 180", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:32:19.830545", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:32:19,889 - sqlalchemy.engine.Engine - INFO - [cached since 516.6s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:32:19.830552', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Updated logger with analysis ID: 180", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:32:19.830545", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:32:19,951 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:32:19,951 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 19:32:20,133 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:32:20,133 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:32:20,133 INFO sqlalchemy.engine.Engine SELECT entity_extraction_analysis.id AS entity_extraction_analysis_id, entity_extraction_analysis.scrape_request_ref_id AS entity_extraction_analysis_scrape_request_ref_id, entity_extraction_analysis.website_url AS entity_extraction_analysis_website_url, entity_extraction_analysis.processing_status AS entity_extraction_analysis_processing_status, entity_extraction_analysis.legal_name AS entity_extraction_analysis_legal_name, entity_extraction_analysis.business_email AS entity_extraction_analysis_business_email, entity_extraction_analysis.support_email AS entity_extraction_analysis_support_email, entity_extraction_analysis.business_contact_numbers AS entity_extraction_analysis_business_contact_numbers, entity_extraction_analysis.business_location AS entity_extraction_analysis_business_location, entity_extraction_analysis.has_jurisdiction_law AS entity_extraction_analysis_has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details AS entity_extraction_analysis_jurisdiction_details, entity_extraction_analysis.accepts_international_orders AS entity_extraction_analysis_accepts_international_orders, entity_extraction_analysis.shipping_policy_details AS entity_extraction_analysis_shipping_policy_details, entity_extraction_analysis.jurisdiction_place AS entity_extraction_analysis_jurisdiction_place, entity_extraction_analysis.shipping_countries AS entity_extraction_analysis_shipping_countries, entity_extraction_analysis.privacy_policy_text AS entity_extraction_analysis_privacy_policy_text, entity_extraction_analysis.terms_conditions_text AS entity_extraction_analysis_terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini AS entity_extraction_analysis_urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini AS entity_extraction_analysis_urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method AS entity_extraction_analysis_extraction_method, entity_extraction_analysis.total_urls_processed AS entity_extraction_analysis_total_urls_processed, entity_extraction_analysis.all_urls_found AS entity_extraction_analysis_all_urls_found, entity_extraction_analysis.reachable_urls AS entity_extraction_analysis_reachable_urls, entity_extraction_analysis.unreachable_urls AS entity_extraction_analysis_unreachable_urls, entity_extraction_analysis.policy_urls_matched AS entity_extraction_analysis_policy_urls_matched, entity_extraction_analysis.created_at AS entity_extraction_analysis_created_at, entity_extraction_analysis.started_at AS entity_extraction_analysis_started_at, entity_extraction_analysis.completed_at AS entity_extraction_analysis_completed_at, entity_extraction_analysis.error_message AS entity_extraction_analysis_error_message, entity_extraction_analysis.org_id AS entity_extraction_analysis_org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.id = %(pk_1)s
2025-08-03 19:32:20,133 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_analysis.id AS entity_extraction_analysis_id, entity_extraction_analysis.scrape_request_ref_id AS entity_extraction_analysis_scrape_request_ref_id, entity_extraction_analysis.website_url AS entity_extraction_analysis_website_url, entity_extraction_analysis.processing_status AS entity_extraction_analysis_processing_status, entity_extraction_analysis.legal_name AS entity_extraction_analysis_legal_name, entity_extraction_analysis.business_email AS entity_extraction_analysis_business_email, entity_extraction_analysis.support_email AS entity_extraction_analysis_support_email, entity_extraction_analysis.business_contact_numbers AS entity_extraction_analysis_business_contact_numbers, entity_extraction_analysis.business_location AS entity_extraction_analysis_business_location, entity_extraction_analysis.has_jurisdiction_law AS entity_extraction_analysis_has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details AS entity_extraction_analysis_jurisdiction_details, entity_extraction_analysis.accepts_international_orders AS entity_extraction_analysis_accepts_international_orders, entity_extraction_analysis.shipping_policy_details AS entity_extraction_analysis_shipping_policy_details, entity_extraction_analysis.jurisdiction_place AS entity_extraction_analysis_jurisdiction_place, entity_extraction_analysis.shipping_countries AS entity_extraction_analysis_shipping_countries, entity_extraction_analysis.privacy_policy_text AS entity_extraction_analysis_privacy_policy_text, entity_extraction_analysis.terms_conditions_text AS entity_extraction_analysis_terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini AS entity_extraction_analysis_urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini AS entity_extraction_analysis_urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method AS entity_extraction_analysis_extraction_method, entity_extraction_analysis.total_urls_processed AS entity_extraction_analysis_total_urls_processed, entity_extraction_analysis.all_urls_found AS entity_extraction_analysis_all_urls_found, entity_extraction_analysis.reachable_urls AS entity_extraction_analysis_reachable_urls, entity_extraction_analysis.unreachable_urls AS entity_extraction_analysis_unreachable_urls, entity_extraction_analysis.policy_urls_matched AS entity_extraction_analysis_policy_urls_matched, entity_extraction_analysis.created_at AS entity_extraction_analysis_created_at, entity_extraction_analysis.started_at AS entity_extraction_analysis_started_at, entity_extraction_analysis.completed_at AS entity_extraction_analysis_completed_at, entity_extraction_analysis.error_message AS entity_extraction_analysis_error_message, entity_extraction_analysis.org_id AS entity_extraction_analysis_org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.id = %(pk_1)s
2025-08-03 19:32:20,133 INFO sqlalchemy.engine.Engine [cached since 514.7s ago] {'pk_1': 180}
2025-08-03 19:32:20,133 - sqlalchemy.engine.Engine - INFO - [cached since 514.7s ago] {'pk_1': 180}
2025-08-03 19:32:20,208 INFO sqlalchemy.engine.Engine UPDATE entity_extraction_analysis SET processing_status=%(processing_status)s, started_at=%(started_at)s WHERE entity_extraction_analysis.id = %(entity_extraction_analysis_id)s
2025-08-03 19:32:20,208 - sqlalchemy.engine.Engine - INFO - UPDATE entity_extraction_analysis SET processing_status=%(processing_status)s, started_at=%(started_at)s WHERE entity_extraction_analysis.id = %(entity_extraction_analysis_id)s
2025-08-03 19:32:20,208 INFO sqlalchemy.engine.Engine [cached since 514.7s ago] {'processing_status': 'IN_PROGRESS', 'started_at': '2025-08-03T19:32:20.071255', 'entity_extraction_analysis_id': 180}
2025-08-03 19:32:20,208 - sqlalchemy.engine.Engine - INFO - [cached since 514.7s ago] {'processing_status': 'IN_PROGRESS', 'started_at': '2025-08-03T19:32:20.071255', 'entity_extraction_analysis_id': 180}
2025-08-03 19:32:20,279 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:32:20,279 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:32:20][EntityExtractor][180][************************************] INFO: Updated analysis 180 status to IN_PROGRESS
2025-08-03 19:32:20,435 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:32:20,435 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:32:20,435 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:20,435 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:20,435 INFO sqlalchemy.engine.Engine [cached since 517.2s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:32:20.380259', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Updated analysis 180 status to IN_PROGRESS", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:32:20.380252", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:32:20,435 - sqlalchemy.engine.Engine - INFO - [cached since 517.2s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:32:20.380259', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Updated analysis 180 status to IN_PROGRESS", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:32:20.380252", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:32:20,490 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:32:20,490 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:32:20][EntityExtractor][url_retrieval_************************************][************************************] INFO: Retrieving policy URLs with reachability status
2025-08-03 19:32:20,650 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:32:20,650 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:32:20,650 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:20,650 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:20,650 INFO sqlalchemy.engine.Engine [cached since 517.4s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:32:20.600291', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Retrieving policy URLs with reachability status", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:32:20.600284", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:32:20,650 - sqlalchemy.engine.Engine - INFO - [cached since 517.4s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:32:20.600291', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Retrieving policy URLs with reachability status", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:32:20.600284", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:32:20,712 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:32:20,712 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:32:21][EntityExtractor][url_retrieval_************************************][************************************] INFO: Found latest scrape_request_ref_id: ************************************ for domain: shell.in
2025-08-03 19:32:21,072 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:32:21,072 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:32:21,072 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:21,072 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:21,072 INFO sqlalchemy.engine.Engine [cached since 517.8s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:32:21.012779', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found latest scrape_request_ref_id: ************************************ for domain: shell.in", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:32:21.012771", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:32:21,072 - sqlalchemy.engine.Engine - INFO - [cached since 517.8s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:32:21.012779', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found latest scrape_request_ref_id: ************************************ for domain: shell.in", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:32:21.012771", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:32:21,143 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:32:21,143 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:32:21][EntityExtractor][url_retrieval_************************************][************************************] INFO: MCC only provided 0 policy types, force-loading 5 missing policies from policy_analysis_new_gemini
2025-08-03 19:32:21,788 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:32:21,788 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:32:21,788 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:21,788 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:21,789 INFO sqlalchemy.engine.Engine [cached since 518.5s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:32:21.733440', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "MCC only provided 0 policy types, force-loading 5 missing policies from policy_analysis_new_gemini", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:32:21.733432", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:32:21,789 - sqlalchemy.engine.Engine - INFO - [cached since 518.5s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:32:21.733440', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "MCC only provided 0 policy types, force-loading 5 missing policies from policy_analysis_new_gemini", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:32:21.733432", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:32:21,852 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:32:21,852 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:32:22][EntityExtractor][url_retrieval_************************************][************************************] INFO: Found latest scrape_request_ref_id: ************************************ for domain: shell.in
2025-08-03 19:32:22,220 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:32:22,220 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:32:22,221 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:22,221 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:22,221 INFO sqlalchemy.engine.Engine [cached since 518.9s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:32:22.158973', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found latest scrape_request_ref_id: ************************************ for domain: shell.in", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:32:22.158962", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:32:22,221 - sqlalchemy.engine.Engine - INFO - [cached since 518.9s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:32:22.158973', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found latest scrape_request_ref_id: ************************************ for domain: shell.in", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:32:22.158962", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:32:22,280 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:32:22,280 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:32:22][EntityExtractor][url_retrieval_************************************][************************************] INFO: Retrieved policy analysis data for 6 policy types
2025-08-03 19:32:22,673 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:32:22,673 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:32:22,673 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:22,673 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:22,673 INFO sqlalchemy.engine.Engine [cached since 519.4s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:32:22.611205', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Retrieved policy analysis data for 6 policy types", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:32:22.611195", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:32:22,673 - sqlalchemy.engine.Engine - INFO - [cached since 519.4s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:32:22.611205', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Retrieved policy analysis data for 6 policy types", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:32:22.611195", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:32:22,731 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:32:22,731 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:32:22][EntityExtractor][url_retrieval_************************************][************************************] INFO: Force-loaded 5 policies from policy_analysis_new_gemini as unreachable
2025-08-03 19:32:23,011 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:32:23,011 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:32:23,011 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:23,011 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:23,011 INFO sqlalchemy.engine.Engine [cached since 519.7s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:32:22.957279', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Force-loaded 5 policies from policy_analysis_new_gemini as unreachable", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:32:22.957272", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:32:23,011 - sqlalchemy.engine.Engine - INFO - [cached since 519.7s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:32:22.957279', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Force-loaded 5 policies from policy_analysis_new_gemini as unreachable", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:32:22.957272", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:32:23,074 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:32:23,074 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:32:23][EntityExtractor][url_retrieval_************************************][************************************] INFO: Total policy URLs retrieved: 5
2025-08-03 19:32:23,287 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:32:23,287 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:32:23,288 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:23,288 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:23,288 INFO sqlalchemy.engine.Engine [cached since 520s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:32:23.188043', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Total policy URLs retrieved: 5", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:32:23.188035", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:32:23,288 - sqlalchemy.engine.Engine - INFO - [cached since 520s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:32:23.188043', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Total policy URLs retrieved: 5", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:32:23.188035", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:32:23,412 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:32:23,412 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:32:23][EntityExtractor][url_retrieval_************************************][************************************] INFO: Found latest scrape_request_ref_id: ************************************ for domain: shell.in
2025-08-03 19:32:23,817 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:32:23,817 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:32:23,818 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:23,818 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:23,818 INFO sqlalchemy.engine.Engine [cached since 520.5s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:32:23.756266', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found latest scrape_request_ref_id: ************************************ for domain: shell.in", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:32:23.756259", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:32:23,818 - sqlalchemy.engine.Engine - INFO - [cached since 520.5s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:32:23.756266', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found latest scrape_request_ref_id: ************************************ for domain: shell.in", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:32:23.756259", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:32:23,881 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:32:23,881 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:32:24][EntityExtractor][url_retrieval_************************************][************************************] INFO: Retrieved policy analysis data for 6 policy types
2025-08-03 19:32:24,253 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:32:24,253 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:32:24,253 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:24,253 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:24,253 INFO sqlalchemy.engine.Engine [cached since 521s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:32:24.188982', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Retrieved policy analysis data for 6 policy types", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:32:24.188972", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:32:24,253 - sqlalchemy.engine.Engine - INFO - [cached since 521s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:32:24.188982', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Retrieved policy analysis data for 6 policy types", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:32:24.188972", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:32:24,311 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:32:24,311 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:32:24][EntityExtractor][180][************************************] INFO: Filtered policy URLs keys: ['privacy_policy', 'terms_and_condition', 'shipping_delivery', 'contact_us', 'about_us']
2025-08-03 19:32:24,590 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:32:24,590 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:32:24,590 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:24,590 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:24,590 INFO sqlalchemy.engine.Engine [cached since 521.3s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:32:24.543310', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Filtered policy URLs keys: [\'privacy_policy\', \'terms_and_condition\', \'shipping_delivery\', \'contact_us\', \'about_us\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:32:24.543302", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:32:24,590 - sqlalchemy.engine.Engine - INFO - [cached since 521.3s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:32:24.543310', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Filtered policy URLs keys: [\'privacy_policy\', \'terms_and_condition\', \'shipping_delivery\', \'contact_us\', \'about_us\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:32:24.543302", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:32:24,652 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:32:24,652 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:32:24][EntityExtractor][180][************************************] INFO: Extracted text length for privacy_policy: 2340
2025-08-03 19:32:24,801 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:32:24,801 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:32:24,801 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:24,801 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:24,801 INFO sqlalchemy.engine.Engine [cached since 521.5s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:32:24.750088', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Extracted text length for privacy_policy: 2340", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:32:24.750079", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:32:24,801 - sqlalchemy.engine.Engine - INFO - [cached since 521.5s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:32:24.750088', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Extracted text length for privacy_policy: 2340", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:32:24.750079", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:32:24,861 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:32:24,861 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:32:24][EntityExtractor][180][************************************] INFO: Extracted text length for terms_and_condition: 1479
2025-08-03 19:32:25,029 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:32:25,029 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:32:25,030 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:25,030 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:25,030 INFO sqlalchemy.engine.Engine [cached since 521.8s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:32:24.981038', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Extracted text length for terms_and_condition: 1479", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:32:24.981028", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:32:25,030 - sqlalchemy.engine.Engine - INFO - [cached since 521.8s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:32:24.981038', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Extracted text length for terms_and_condition: 1479", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:32:24.981028", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:32:25,107 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:32:25,107 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:32:25][EntityExtractor][url_retrieval_************************************][************************************] INFO: Checking Gemini reachability for 5 URLs
2025-08-03 19:32:25,261 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:32:25,261 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:32:25,261 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:25,261 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:25,261 INFO sqlalchemy.engine.Engine [cached since 522s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:32:25.210450', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Checking Gemini reachability for 5 URLs", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:32:25.210440", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:32:25,261 - sqlalchemy.engine.Engine - INFO - [cached since 522s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:32:25.210450', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Checking Gemini reachability for 5 URLs", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:32:25.210440", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:32:25,321 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:32:25,321 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:32:25][EntityExtractor][url_retrieval_************************************][************************************] INFO: Gemini reachability check completed: 5 reachable, 0 unreachable
2025-08-03 19:32:25,475 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:32:25,475 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:32:25,476 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:25,476 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:25,476 INFO sqlalchemy.engine.Engine [cached since 522.2s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:32:25.421900', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Gemini reachability check completed: 5 reachable, 0 unreachable", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:32:25.421892", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:32:25,476 - sqlalchemy.engine.Engine - INFO - [cached since 522.2s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:32:25.421900', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Gemini reachability check completed: 5 reachable, 0 unreachable", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:32:25.421892", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:32:25,523 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:32:25,523 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 19:32:27,649 - google_genai.models - INFO - AFC is enabled with max remote calls: 20000.
2025-08-03 19:32:50,833 - httpx - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent "HTTP/1.1 200 OK"
2025-08-03 19:32:50,834 - google_genai.models - INFO - AFC remote call 1 is done.
[2025-08-03 19:32:27][legacy_unknown][unknown] INFO: Starting Gemini API call
{
  "model": "gemini-2.5-flash",
  "timeout": 120,
  "max_retries": 3,
  "prompt_length": 5395,
  "context": {
    "task_type": "legacy"
  }
}
[2025-08-03 19:32:27][legacy_unknown][unknown] INFO: Gemini API attempt 1/3
[2025-08-03 19:32:50][legacy_unknown][unknown] INFO: Gemini API Usage: cache_tokens_details=None cached_content_token_count=None candidates_token_count=350 candidates_tokens_details=None prompt_token_count=1281 prompt_tokens_details=[ModalityTokenCount(
  modality=<MediaModality.TEXT: 'TEXT'>,
  token_count=1281
)] thoughts_token_count=2875 tool_use_prompt_token_count=None tool_use_prompt_tokens_details=None total_token_count=4506 traffic_type=None
[2025-08-03 19:32:50][legacy_unknown][unknown] INFO: Gemini API call successful
{
  "attempt": 1,
  "response_length": 749,
  "finish_reason": "STOP"
}
[2025-08-03 19:32:52][api_response_logger][unknown] INFO: Gemini API Call - Model: gemini-2.5-flash, Request ID: None
{
  "response_preview": "```json\n{\n    \"legal_name\": \"Shell India Markets Private Limited\",\n    \"business_email\": [\"<EMAIL>\"],\n    \"support_email\": [],\n    \"business_contact_numbers\": [\"044-3099 11...",
  "log_file": "api_logs/gemini_20250803_193252_836.json",
  "clean_file": "api_logs/clean_gemini_20250803_193252_836.json"
}
[API_LOGGER] Gemini response logged to: api_logs/gemini_20250803_193252_836.json
[API_LOGGER] Clean version at: api_logs/clean_gemini_20250803_193252_836.json
[2025-08-03 19:32:52][api_response_logger][unknown] INFO: Gemini API Call - Model: gemini-2.5-flash, Request ID: ************************************
{
  "response_preview": "```json\n{\n    \"legal_name\": \"Shell India Markets Private Limited\",\n    \"business_email\": [\"<EMAIL>\"],\n    \"support_email\": [],\n    \"business_contact_numbers\": [\"044-3099 11...",
  "log_file": "api_logs/gemini_20250803_193252_837_************************************.json",
  "clean_file": "api_logs/clean_gemini_20250803_193252_837_************************************.json"
}
[API_LOGGER] Gemini response logged to: api_logs/gemini_20250803_193252_837_************************************.json
[API_LOGGER] Clean version at: api_logs/clean_gemini_20250803_193252_837_************************************.json
2025-08-03 19:32:52,894 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:32:52,894 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:32:52,894 INFO sqlalchemy.engine.Engine SELECT entity_extraction_url_analysis.id, entity_extraction_url_analysis.analysis_id, entity_extraction_url_analysis.entity_analysis_id, entity_extraction_url_analysis.scrape_request_ref_id, entity_extraction_url_analysis.url, entity_extraction_url_analysis.url_type, entity_extraction_url_analysis.url_classification, entity_extraction_url_analysis.url_source_table, entity_extraction_url_analysis.is_reachable_by_gemini, entity_extraction_url_analysis.soft_classification, entity_extraction_url_analysis.hard_classification, entity_extraction_url_analysis.extracted_text, entity_extraction_url_analysis.extracted_text_length, entity_extraction_url_analysis.extracted_entities, entity_extraction_url_analysis.processing_method, entity_extraction_url_analysis.processing_status, entity_extraction_url_analysis.error_message, entity_extraction_url_analysis.created_at, entity_extraction_url_analysis.processed_at, entity_extraction_url_analysis.org_id 
FROM entity_extraction_url_analysis 
WHERE entity_extraction_url_analysis.analysis_id = %(analysis_id_1)s AND entity_extraction_url_analysis.url = %(url_1)s
2025-08-03 19:32:52,894 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_url_analysis.id, entity_extraction_url_analysis.analysis_id, entity_extraction_url_analysis.entity_analysis_id, entity_extraction_url_analysis.scrape_request_ref_id, entity_extraction_url_analysis.url, entity_extraction_url_analysis.url_type, entity_extraction_url_analysis.url_classification, entity_extraction_url_analysis.url_source_table, entity_extraction_url_analysis.is_reachable_by_gemini, entity_extraction_url_analysis.soft_classification, entity_extraction_url_analysis.hard_classification, entity_extraction_url_analysis.extracted_text, entity_extraction_url_analysis.extracted_text_length, entity_extraction_url_analysis.extracted_entities, entity_extraction_url_analysis.processing_method, entity_extraction_url_analysis.processing_status, entity_extraction_url_analysis.error_message, entity_extraction_url_analysis.created_at, entity_extraction_url_analysis.processed_at, entity_extraction_url_analysis.org_id 
FROM entity_extraction_url_analysis 
WHERE entity_extraction_url_analysis.analysis_id = %(analysis_id_1)s AND entity_extraction_url_analysis.url = %(url_1)s
2025-08-03 19:32:52,894 INFO sqlalchemy.engine.Engine [cached since 518.8s ago] {'analysis_id_1': 180, 'url_1': 'https://www.shell.in/privacy.html'}
2025-08-03 19:32:52,894 - sqlalchemy.engine.Engine - INFO - [cached since 518.8s ago] {'analysis_id_1': 180, 'url_1': 'https://www.shell.in/privacy.html'}
[2025-08-03 19:32:52][EntityExtractor][None][************************************] WARNING: URL analysis record not found for URL: https://www.shell.in/privacy.html
2025-08-03 19:32:53,004 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:32:53,004 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:32:53,004 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:53,004 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:53,004 INFO sqlalchemy.engine.Engine [cached since 549.7s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:32:52.952258', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "URL analysis record not found for URL: https://www.shell.in/privacy.html", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:32:52.952252", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:32:53,004 - sqlalchemy.engine.Engine - INFO - [cached since 549.7s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:32:52.952258', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "URL analysis record not found for URL: https://www.shell.in/privacy.html", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:32:52.952252", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:32:53,061 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:32:53,061 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 19:32:53,171 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-03 19:32:53,171 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-03 19:32:53,392 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:32:53,392 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:32:53,392 INFO sqlalchemy.engine.Engine SELECT entity_extraction_url_analysis.id, entity_extraction_url_analysis.analysis_id, entity_extraction_url_analysis.entity_analysis_id, entity_extraction_url_analysis.scrape_request_ref_id, entity_extraction_url_analysis.url, entity_extraction_url_analysis.url_type, entity_extraction_url_analysis.url_classification, entity_extraction_url_analysis.url_source_table, entity_extraction_url_analysis.is_reachable_by_gemini, entity_extraction_url_analysis.soft_classification, entity_extraction_url_analysis.hard_classification, entity_extraction_url_analysis.extracted_text, entity_extraction_url_analysis.extracted_text_length, entity_extraction_url_analysis.extracted_entities, entity_extraction_url_analysis.processing_method, entity_extraction_url_analysis.processing_status, entity_extraction_url_analysis.error_message, entity_extraction_url_analysis.created_at, entity_extraction_url_analysis.processed_at, entity_extraction_url_analysis.org_id 
FROM entity_extraction_url_analysis 
WHERE entity_extraction_url_analysis.analysis_id = %(analysis_id_1)s AND entity_extraction_url_analysis.url = %(url_1)s
2025-08-03 19:32:53,392 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_url_analysis.id, entity_extraction_url_analysis.analysis_id, entity_extraction_url_analysis.entity_analysis_id, entity_extraction_url_analysis.scrape_request_ref_id, entity_extraction_url_analysis.url, entity_extraction_url_analysis.url_type, entity_extraction_url_analysis.url_classification, entity_extraction_url_analysis.url_source_table, entity_extraction_url_analysis.is_reachable_by_gemini, entity_extraction_url_analysis.soft_classification, entity_extraction_url_analysis.hard_classification, entity_extraction_url_analysis.extracted_text, entity_extraction_url_analysis.extracted_text_length, entity_extraction_url_analysis.extracted_entities, entity_extraction_url_analysis.processing_method, entity_extraction_url_analysis.processing_status, entity_extraction_url_analysis.error_message, entity_extraction_url_analysis.created_at, entity_extraction_url_analysis.processed_at, entity_extraction_url_analysis.org_id 
FROM entity_extraction_url_analysis 
WHERE entity_extraction_url_analysis.analysis_id = %(analysis_id_1)s AND entity_extraction_url_analysis.url = %(url_1)s
2025-08-03 19:32:53,392 INFO sqlalchemy.engine.Engine [cached since 519.3s ago] {'analysis_id_1': 180, 'url_1': 'https://www.shell.in/terms-and-conditions.html'}
2025-08-03 19:32:53,392 - sqlalchemy.engine.Engine - INFO - [cached since 519.3s ago] {'analysis_id_1': 180, 'url_1': 'https://www.shell.in/terms-and-conditions.html'}
[2025-08-03 19:32:53][EntityExtractor][None][************************************] WARNING: URL analysis record not found for URL: https://www.shell.in/terms-and-conditions.html
2025-08-03 19:32:53,583 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:32:53,583 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:32:53,584 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:53,584 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:53,584 INFO sqlalchemy.engine.Engine [cached since 550.3s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:32:53.532689', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "URL analysis record not found for URL: https://www.shell.in/terms-and-conditions.html", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:32:53.532679", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:32:53,584 - sqlalchemy.engine.Engine - INFO - [cached since 550.3s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:32:53.532689', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "URL analysis record not found for URL: https://www.shell.in/terms-and-conditions.html", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:32:53.532679", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:32:53,648 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:32:53,648 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 19:32:53,751 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-03 19:32:53,751 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-03 19:32:53,911 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:32:53,911 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:32:53,911 INFO sqlalchemy.engine.Engine SELECT entity_extraction_url_analysis.id, entity_extraction_url_analysis.analysis_id, entity_extraction_url_analysis.entity_analysis_id, entity_extraction_url_analysis.scrape_request_ref_id, entity_extraction_url_analysis.url, entity_extraction_url_analysis.url_type, entity_extraction_url_analysis.url_classification, entity_extraction_url_analysis.url_source_table, entity_extraction_url_analysis.is_reachable_by_gemini, entity_extraction_url_analysis.soft_classification, entity_extraction_url_analysis.hard_classification, entity_extraction_url_analysis.extracted_text, entity_extraction_url_analysis.extracted_text_length, entity_extraction_url_analysis.extracted_entities, entity_extraction_url_analysis.processing_method, entity_extraction_url_analysis.processing_status, entity_extraction_url_analysis.error_message, entity_extraction_url_analysis.created_at, entity_extraction_url_analysis.processed_at, entity_extraction_url_analysis.org_id 
FROM entity_extraction_url_analysis 
WHERE entity_extraction_url_analysis.analysis_id = %(analysis_id_1)s AND entity_extraction_url_analysis.url = %(url_1)s
2025-08-03 19:32:53,911 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_url_analysis.id, entity_extraction_url_analysis.analysis_id, entity_extraction_url_analysis.entity_analysis_id, entity_extraction_url_analysis.scrape_request_ref_id, entity_extraction_url_analysis.url, entity_extraction_url_analysis.url_type, entity_extraction_url_analysis.url_classification, entity_extraction_url_analysis.url_source_table, entity_extraction_url_analysis.is_reachable_by_gemini, entity_extraction_url_analysis.soft_classification, entity_extraction_url_analysis.hard_classification, entity_extraction_url_analysis.extracted_text, entity_extraction_url_analysis.extracted_text_length, entity_extraction_url_analysis.extracted_entities, entity_extraction_url_analysis.processing_method, entity_extraction_url_analysis.processing_status, entity_extraction_url_analysis.error_message, entity_extraction_url_analysis.created_at, entity_extraction_url_analysis.processed_at, entity_extraction_url_analysis.org_id 
FROM entity_extraction_url_analysis 
WHERE entity_extraction_url_analysis.analysis_id = %(analysis_id_1)s AND entity_extraction_url_analysis.url = %(url_1)s
2025-08-03 19:32:53,911 INFO sqlalchemy.engine.Engine [cached since 519.8s ago] {'analysis_id_1': 180, 'url_1': 'not_found'}
2025-08-03 19:32:53,911 - sqlalchemy.engine.Engine - INFO - [cached since 519.8s ago] {'analysis_id_1': 180, 'url_1': 'not_found'}
[2025-08-03 19:32:53][EntityExtractor][None][************************************] WARNING: URL analysis record not found for URL: not_found
2025-08-03 19:32:54,041 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:32:54,041 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:32:54,042 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:54,042 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:54,042 INFO sqlalchemy.engine.Engine [cached since 550.8s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:32:53.974425', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "URL analysis record not found for URL: not_found", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:32:53.974419", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:32:54,042 - sqlalchemy.engine.Engine - INFO - [cached since 550.8s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:32:53.974425', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "URL analysis record not found for URL: not_found", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:32:53.974419", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:32:54,101 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:32:54,101 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 19:32:54,215 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-03 19:32:54,215 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-03 19:32:54,526 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:32:54,526 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:32:54,527 INFO sqlalchemy.engine.Engine SELECT entity_extraction_url_analysis.id, entity_extraction_url_analysis.analysis_id, entity_extraction_url_analysis.entity_analysis_id, entity_extraction_url_analysis.scrape_request_ref_id, entity_extraction_url_analysis.url, entity_extraction_url_analysis.url_type, entity_extraction_url_analysis.url_classification, entity_extraction_url_analysis.url_source_table, entity_extraction_url_analysis.is_reachable_by_gemini, entity_extraction_url_analysis.soft_classification, entity_extraction_url_analysis.hard_classification, entity_extraction_url_analysis.extracted_text, entity_extraction_url_analysis.extracted_text_length, entity_extraction_url_analysis.extracted_entities, entity_extraction_url_analysis.processing_method, entity_extraction_url_analysis.processing_status, entity_extraction_url_analysis.error_message, entity_extraction_url_analysis.created_at, entity_extraction_url_analysis.processed_at, entity_extraction_url_analysis.org_id 
FROM entity_extraction_url_analysis 
WHERE entity_extraction_url_analysis.analysis_id = %(analysis_id_1)s AND entity_extraction_url_analysis.url = %(url_1)s
2025-08-03 19:32:54,527 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_url_analysis.id, entity_extraction_url_analysis.analysis_id, entity_extraction_url_analysis.entity_analysis_id, entity_extraction_url_analysis.scrape_request_ref_id, entity_extraction_url_analysis.url, entity_extraction_url_analysis.url_type, entity_extraction_url_analysis.url_classification, entity_extraction_url_analysis.url_source_table, entity_extraction_url_analysis.is_reachable_by_gemini, entity_extraction_url_analysis.soft_classification, entity_extraction_url_analysis.hard_classification, entity_extraction_url_analysis.extracted_text, entity_extraction_url_analysis.extracted_text_length, entity_extraction_url_analysis.extracted_entities, entity_extraction_url_analysis.processing_method, entity_extraction_url_analysis.processing_status, entity_extraction_url_analysis.error_message, entity_extraction_url_analysis.created_at, entity_extraction_url_analysis.processed_at, entity_extraction_url_analysis.org_id 
FROM entity_extraction_url_analysis 
WHERE entity_extraction_url_analysis.analysis_id = %(analysis_id_1)s AND entity_extraction_url_analysis.url = %(url_1)s
2025-08-03 19:32:54,527 INFO sqlalchemy.engine.Engine [cached since 520.5s ago] {'analysis_id_1': 180, 'url_1': 'https://www.shell.in/about-us/contact-us.html'}
2025-08-03 19:32:54,527 - sqlalchemy.engine.Engine - INFO - [cached since 520.5s ago] {'analysis_id_1': 180, 'url_1': 'https://www.shell.in/about-us/contact-us.html'}
[2025-08-03 19:32:54][EntityExtractor][None][************************************] WARNING: URL analysis record not found for URL: https://www.shell.in/about-us/contact-us.html
2025-08-03 19:32:54,650 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:32:54,650 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:32:54,651 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:54,651 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:54,651 INFO sqlalchemy.engine.Engine [cached since 551.4s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:32:54.593141', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "URL analysis record not found for URL: https://www.shell.in/about-us/contact-us.html", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:32:54.593135", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:32:54,651 - sqlalchemy.engine.Engine - INFO - [cached since 551.4s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:32:54.593141', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "URL analysis record not found for URL: https://www.shell.in/about-us/contact-us.html", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:32:54.593135", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:32:54,711 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:32:54,711 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 19:32:54,820 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-03 19:32:54,820 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-03 19:32:54,970 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:32:54,970 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:32:54,971 INFO sqlalchemy.engine.Engine SELECT entity_extraction_url_analysis.id, entity_extraction_url_analysis.analysis_id, entity_extraction_url_analysis.entity_analysis_id, entity_extraction_url_analysis.scrape_request_ref_id, entity_extraction_url_analysis.url, entity_extraction_url_analysis.url_type, entity_extraction_url_analysis.url_classification, entity_extraction_url_analysis.url_source_table, entity_extraction_url_analysis.is_reachable_by_gemini, entity_extraction_url_analysis.soft_classification, entity_extraction_url_analysis.hard_classification, entity_extraction_url_analysis.extracted_text, entity_extraction_url_analysis.extracted_text_length, entity_extraction_url_analysis.extracted_entities, entity_extraction_url_analysis.processing_method, entity_extraction_url_analysis.processing_status, entity_extraction_url_analysis.error_message, entity_extraction_url_analysis.created_at, entity_extraction_url_analysis.processed_at, entity_extraction_url_analysis.org_id 
FROM entity_extraction_url_analysis 
WHERE entity_extraction_url_analysis.analysis_id = %(analysis_id_1)s AND entity_extraction_url_analysis.url = %(url_1)s
2025-08-03 19:32:54,971 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_url_analysis.id, entity_extraction_url_analysis.analysis_id, entity_extraction_url_analysis.entity_analysis_id, entity_extraction_url_analysis.scrape_request_ref_id, entity_extraction_url_analysis.url, entity_extraction_url_analysis.url_type, entity_extraction_url_analysis.url_classification, entity_extraction_url_analysis.url_source_table, entity_extraction_url_analysis.is_reachable_by_gemini, entity_extraction_url_analysis.soft_classification, entity_extraction_url_analysis.hard_classification, entity_extraction_url_analysis.extracted_text, entity_extraction_url_analysis.extracted_text_length, entity_extraction_url_analysis.extracted_entities, entity_extraction_url_analysis.processing_method, entity_extraction_url_analysis.processing_status, entity_extraction_url_analysis.error_message, entity_extraction_url_analysis.created_at, entity_extraction_url_analysis.processed_at, entity_extraction_url_analysis.org_id 
FROM entity_extraction_url_analysis 
WHERE entity_extraction_url_analysis.analysis_id = %(analysis_id_1)s AND entity_extraction_url_analysis.url = %(url_1)s
2025-08-03 19:32:54,971 INFO sqlalchemy.engine.Engine [cached since 520.9s ago] {'analysis_id_1': 180, 'url_1': 'https://www.shell.in/about-us/careers.html'}
2025-08-03 19:32:54,971 - sqlalchemy.engine.Engine - INFO - [cached since 520.9s ago] {'analysis_id_1': 180, 'url_1': 'https://www.shell.in/about-us/careers.html'}
[2025-08-03 19:32:55][EntityExtractor][None][************************************] WARNING: URL analysis record not found for URL: https://www.shell.in/about-us/careers.html
2025-08-03 19:32:55,080 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:32:55,080 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:32:55,080 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:55,080 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:55,080 INFO sqlalchemy.engine.Engine [cached since 551.8s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:32:55.031595', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "URL analysis record not found for URL: https://www.shell.in/about-us/careers.html", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:32:55.031589", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:32:55,080 - sqlalchemy.engine.Engine - INFO - [cached since 551.8s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:32:55.031595', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "URL analysis record not found for URL: https://www.shell.in/about-us/careers.html", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:32:55.031589", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:32:55,144 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:32:55,144 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 19:32:55,239 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-03 19:32:55,239 - sqlalchemy.engine.Engine - INFO - ROLLBACK
[2025-08-03 19:32:55][EntityExtractor][180][************************************] INFO: Gemini extraction rate 72.7% < 90%, forcing backup flow for all fields
2025-08-03 19:32:55,410 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:32:55,410 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:32:55,410 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:55,410 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:55,410 INFO sqlalchemy.engine.Engine [cached since 552.1s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:32:55.358054', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Gemini extraction rate 72.7% < 90%, forcing backup flow for all fields", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:32:55.358047", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:32:55,410 - sqlalchemy.engine.Engine - INFO - [cached since 552.1s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:32:55.358054', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Gemini extraction rate 72.7% < 90%, forcing backup flow for all fields", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:32:55.358047", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:32:55,471 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:32:55,471 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:32:55][EntityExtractor][180][************************************] INFO: Gemini missing fields to fallback: ['support_email', 'shipping_countries', 'shipping_policy_details']
2025-08-03 19:32:55,620 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:32:55,620 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:32:55,620 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:55,620 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:55,620 INFO sqlalchemy.engine.Engine [cached since 552.3s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:32:55.570374', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Gemini missing fields to fallback: [\'support_email\', \'shipping_countries\', \'shipping_policy_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:32:55.570367", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:32:55,620 - sqlalchemy.engine.Engine - INFO - [cached since 552.3s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:32:55.570374', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Gemini missing fields to fallback: [\'support_email\', \'shipping_countries\', \'shipping_policy_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:32:55.570367", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:32:55,697 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:32:55,697 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:32:55][EntityExtractor][180][************************************] INFO: Starting OpenAI backup flow for missing fields: ['support_email', 'shipping_countries', 'shipping_policy_details']
2025-08-03 19:32:55,842 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:32:55,842 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:32:55,842 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:55,842 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:55,842 INFO sqlalchemy.engine.Engine [cached since 552.6s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:32:55.790463', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Starting OpenAI backup flow for missing fields: [\'support_email\', \'shipping_countries\', \'shipping_policy_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:32:55.790455", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:32:55,842 - sqlalchemy.engine.Engine - INFO - [cached since 552.6s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:32:55.790463', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Starting OpenAI backup flow for missing fields: [\'support_email\', \'shipping_countries\', \'shipping_policy_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:32:55.790455", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:32:55,916 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:32:55,916 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 19:32:56,088 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:32:56,088 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:32:56,089 INFO sqlalchemy.engine.Engine SELECT policy_analysis_new_gemini.id, policy_analysis_new_gemini.website, policy_analysis_new_gemini.scrape_request_ref_id, policy_analysis_new_gemini.analysis_flow_used, policy_analysis_new_gemini.reachability_percentage, policy_analysis_new_gemini.total_urls_processed, policy_analysis_new_gemini.privacy_policy_url, policy_analysis_new_gemini.terms_and_condition_url, policy_analysis_new_gemini.shipping_delivery_url, policy_analysis_new_gemini.returns_cancellation_exchange_url, policy_analysis_new_gemini.contact_us_url, policy_analysis_new_gemini.home_page_url, policy_analysis_new_gemini.about_us_url, policy_analysis_new_gemini.privacy_policy_text, policy_analysis_new_gemini.terms_and_condition_text, policy_analysis_new_gemini.shipping_delivery_text, policy_analysis_new_gemini.returns_cancellation_exchange_text, policy_analysis_new_gemini.contact_us_text, policy_analysis_new_gemini.home_page_text, policy_analysis_new_gemini.about_us_text, policy_analysis_new_gemini.privacy_policy_screenshot, policy_analysis_new_gemini.terms_and_condition_screenshot, policy_analysis_new_gemini.shipping_delivery_screenshot, policy_analysis_new_gemini.returns_cancellation_exchange_screenshot, policy_analysis_new_gemini.contact_us_screenshot, policy_analysis_new_gemini.home_page_screenshot, policy_analysis_new_gemini.about_us_screenshot, policy_analysis_new_gemini.instagram_url, policy_analysis_new_gemini.instagram_text, policy_analysis_new_gemini.instagram_screenshot, policy_analysis_new_gemini.youtube_url, policy_analysis_new_gemini.youtube_text, policy_analysis_new_gemini.youtube_screenshot, policy_analysis_new_gemini.facebook_url, policy_analysis_new_gemini.facebook_text, policy_analysis_new_gemini.facebook_screenshot, policy_analysis_new_gemini.twitter_url, policy_analysis_new_gemini.twitter_text, policy_analysis_new_gemini.twitter_screenshot, policy_analysis_new_gemini.linkedin_url, policy_analysis_new_gemini.linkedin_text, policy_analysis_new_gemini.linkedin_screenshot, policy_analysis_new_gemini.pinterest_url, policy_analysis_new_gemini.pinterest_text, policy_analysis_new_gemini.pinterest_screenshot, policy_analysis_new_gemini.x_url, policy_analysis_new_gemini.x_text, policy_analysis_new_gemini.x_screenshot, policy_analysis_new_gemini.result_status, policy_analysis_new_gemini.failed_at, policy_analysis_new_gemini.last_updated, policy_analysis_new_gemini.details, policy_analysis_new_gemini.processing_status, policy_analysis_new_gemini.created_at, policy_analysis_new_gemini.started_at, policy_analysis_new_gemini.completed_at, policy_analysis_new_gemini.error_message, policy_analysis_new_gemini.org_id 
FROM policy_analysis_new_gemini 
WHERE policy_analysis_new_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s ORDER BY policy_analysis_new_gemini.id DESC
2025-08-03 19:32:56,089 - sqlalchemy.engine.Engine - INFO - SELECT policy_analysis_new_gemini.id, policy_analysis_new_gemini.website, policy_analysis_new_gemini.scrape_request_ref_id, policy_analysis_new_gemini.analysis_flow_used, policy_analysis_new_gemini.reachability_percentage, policy_analysis_new_gemini.total_urls_processed, policy_analysis_new_gemini.privacy_policy_url, policy_analysis_new_gemini.terms_and_condition_url, policy_analysis_new_gemini.shipping_delivery_url, policy_analysis_new_gemini.returns_cancellation_exchange_url, policy_analysis_new_gemini.contact_us_url, policy_analysis_new_gemini.home_page_url, policy_analysis_new_gemini.about_us_url, policy_analysis_new_gemini.privacy_policy_text, policy_analysis_new_gemini.terms_and_condition_text, policy_analysis_new_gemini.shipping_delivery_text, policy_analysis_new_gemini.returns_cancellation_exchange_text, policy_analysis_new_gemini.contact_us_text, policy_analysis_new_gemini.home_page_text, policy_analysis_new_gemini.about_us_text, policy_analysis_new_gemini.privacy_policy_screenshot, policy_analysis_new_gemini.terms_and_condition_screenshot, policy_analysis_new_gemini.shipping_delivery_screenshot, policy_analysis_new_gemini.returns_cancellation_exchange_screenshot, policy_analysis_new_gemini.contact_us_screenshot, policy_analysis_new_gemini.home_page_screenshot, policy_analysis_new_gemini.about_us_screenshot, policy_analysis_new_gemini.instagram_url, policy_analysis_new_gemini.instagram_text, policy_analysis_new_gemini.instagram_screenshot, policy_analysis_new_gemini.youtube_url, policy_analysis_new_gemini.youtube_text, policy_analysis_new_gemini.youtube_screenshot, policy_analysis_new_gemini.facebook_url, policy_analysis_new_gemini.facebook_text, policy_analysis_new_gemini.facebook_screenshot, policy_analysis_new_gemini.twitter_url, policy_analysis_new_gemini.twitter_text, policy_analysis_new_gemini.twitter_screenshot, policy_analysis_new_gemini.linkedin_url, policy_analysis_new_gemini.linkedin_text, policy_analysis_new_gemini.linkedin_screenshot, policy_analysis_new_gemini.pinterest_url, policy_analysis_new_gemini.pinterest_text, policy_analysis_new_gemini.pinterest_screenshot, policy_analysis_new_gemini.x_url, policy_analysis_new_gemini.x_text, policy_analysis_new_gemini.x_screenshot, policy_analysis_new_gemini.result_status, policy_analysis_new_gemini.failed_at, policy_analysis_new_gemini.last_updated, policy_analysis_new_gemini.details, policy_analysis_new_gemini.processing_status, policy_analysis_new_gemini.created_at, policy_analysis_new_gemini.started_at, policy_analysis_new_gemini.completed_at, policy_analysis_new_gemini.error_message, policy_analysis_new_gemini.org_id 
FROM policy_analysis_new_gemini 
WHERE policy_analysis_new_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s ORDER BY policy_analysis_new_gemini.id DESC
2025-08-03 19:32:56,089 INFO sqlalchemy.engine.Engine [cached since 519s ago] {'scrape_request_ref_id_1': '************************************'}
2025-08-03 19:32:56,089 - sqlalchemy.engine.Engine - INFO - [cached since 519s ago] {'scrape_request_ref_id_1': '************************************'}
[2025-08-03 19:32:56][EntityExtractor][180][************************************] INFO: Retrieved policy texts: ['privacy_policy', 'terms_and_condition', 'contact_us', 'about_us']
2025-08-03 19:32:56,248 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:32:56,248 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:32:56,248 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:56,248 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:56,248 INFO sqlalchemy.engine.Engine [cached since 553s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:32:56.185441', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Retrieved policy texts: [\'privacy_policy\', \'terms_and_condition\', \'contact_us\', \'about_us\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:32:56.185433", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:32:56,248 - sqlalchemy.engine.Engine - INFO - [cached since 553s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:32:56.185441', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Retrieved policy texts: [\'privacy_policy\', \'terms_and_condition\', \'contact_us\', \'about_us\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:32:56.185433", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:32:56,320 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:32:56,320 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 19:32:56,426 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-03 19:32:56,426 - sqlalchemy.engine.Engine - INFO - ROLLBACK
[2025-08-03 19:32:56][EntityExtractor][180][************************************] INFO: Found policy texts for 4 policy types
2025-08-03 19:32:56,602 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:32:56,602 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:32:56,602 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:56,602 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:56,602 INFO sqlalchemy.engine.Engine [cached since 553.3s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:32:56.540953', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found policy texts for 4 policy types", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:32:56.540946", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:32:56,602 - sqlalchemy.engine.Engine - INFO - [cached since 553.3s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:32:56.540953', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found policy texts for 4 policy types", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:32:56.540946", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:32:56,661 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:32:56,661 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:32:56][EntityExtractor][180][************************************] INFO: Processing contact_info with fields: ['support_email']
2025-08-03 19:32:56,821 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:32:56,821 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:32:56,821 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:56,821 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:56,821 INFO sqlalchemy.engine.Engine [cached since 553.5s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:32:56.771225', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Processing contact_info with fields: [\'support_email\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:32:56.771217", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:32:56,821 - sqlalchemy.engine.Engine - INFO - [cached since 553.5s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:32:56.771225', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Processing contact_info with fields: [\'support_email\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:32:56.771217", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:32:56,880 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:32:56,880 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 19:32:58,167 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 19:32:58,220 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:32:58,220 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:32:58,220 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:58,220 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:58,220 INFO sqlalchemy.engine.Engine [cached since 554.9s ago] {'analysis_id': 0, 'timestamp': '2025-08-03 19:32:56', 'type': 'openai', 'messages': '[\n    {\n        "role": "user",\n        "content": "\\nYou are an expert at extracting business information from policy documents and website cont ... (10420 characters truncated) ... \\n{\\n    \\"support_email\\": \\"extracted support/customer email or null\\"\\n}\\n\\nReturn only the JSON response, no additional text."\n    }\n]', 'response': '"{\\n    \\"support_email\\": \\"<EMAIL>\\"\\n}"', 'org_id': 'default'}
2025-08-03 19:32:58,220 - sqlalchemy.engine.Engine - INFO - [cached since 554.9s ago] {'analysis_id': 0, 'timestamp': '2025-08-03 19:32:56', 'type': 'openai', 'messages': '[\n    {\n        "role": "user",\n        "content": "\\nYou are an expert at extracting business information from policy documents and website cont ... (10420 characters truncated) ... \\n{\\n    \\"support_email\\": \\"extracted support/customer email or null\\"\\n}\\n\\nReturn only the JSON response, no additional text."\n    }\n]', 'response': '"{\\n    \\"support_email\\": \\"<EMAIL>\\"\\n}"', 'org_id': 'default'}
2025-08-03 19:32:58,294 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:32:58,294 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:32:58][api_response_logger][unknown] INFO: OpenAI API Call - Name: entity_extraction_contact_info, Request ID: None
{
  "response_preview": "{\n    \"support_email\": \"<EMAIL>\"\n}",
  "log_file": "api_logs/openai_20250803_193258_390.json",
  "clean_file": "api_logs/clean_openai_20250803_193258_390.json"
}
[API_LOGGER] OpenAI response logged to: api_logs/openai_20250803_193258_390.json
[API_LOGGER] Clean version at: api_logs/clean_openai_20250803_193258_390.json
[2025-08-03 19:32:58][api_response_logger][unknown] INFO: OpenAI API Call - Name: entity_extraction_contact_info, Request ID: ************************************
{
  "response_preview": "{\n    \"support_email\": \"<EMAIL>\"\n}",
  "log_file": "api_logs/openai_20250803_193258_391_************************************.json",
  "clean_file": "api_logs/clean_openai_20250803_193258_391_************************************.json"
}
[API_LOGGER] OpenAI response logged to: api_logs/openai_20250803_193258_391_************************************.json
[API_LOGGER] Clean version at: api_logs/clean_openai_20250803_193258_391_************************************.json
[2025-08-03 19:32:58][EntityExtractor][180][************************************] INFO: OpenAI contact_info call extracted 1 fields: ['support_email']
2025-08-03 19:32:58,441 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:32:58,441 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:32:58,441 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:58,441 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:58,441 INFO sqlalchemy.engine.Engine [cached since 555.2s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:32:58.391694', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "OpenAI contact_info call extracted 1 fields: [\'support_email\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:32:58.391688", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:32:58,441 - sqlalchemy.engine.Engine - INFO - [cached since 555.2s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:32:58.391694', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "OpenAI contact_info call extracted 1 fields: [\'support_email\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:32:58.391688", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:32:58,510 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:32:58,510 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:32:58][EntityExtractor][180][************************************] INFO: Processing shipping_info with fields: ['shipping_countries', 'shipping_policy_details']
2025-08-03 19:32:58,680 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:32:58,680 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:32:58,681 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:58,681 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:32:58,681 INFO sqlalchemy.engine.Engine [cached since 555.4s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:32:58.631030', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Processing shipping_info with fields: [\'shipping_countries\', \'shipping_policy_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:32:58.631021", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:32:58,681 - sqlalchemy.engine.Engine - INFO - [cached since 555.4s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:32:58.631030', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Processing shipping_info with fields: [\'shipping_countries\', \'shipping_policy_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:32:58.631021", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:32:58,741 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:32:58,741 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 19:33:00,786 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 19:33:00,850 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:33:00,850 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:33:00,851 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:33:00,851 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:33:00,851 INFO sqlalchemy.engine.Engine [cached since 557.6s ago] {'analysis_id': 0, 'timestamp': '2025-08-03 19:32:58', 'type': 'openai', 'messages': '[\n    {\n        "role": "user",\n        "content": "\\nYou are an expert at extracting business information from policy documents and website cont ... (2726 characters truncated) ... \\"shipping_policy_details\\": \\"extracted shipping policy details or null\\"\\n}\\n\\nReturn only the JSON response, no additional text."\n    }\n]', 'response': '"{\\n    \\"shipping_countries\\": \\"null\\",\\n    \\"shipping_policy_details\\": \\"null\\"\\n}"', 'org_id': 'default'}
2025-08-03 19:33:00,851 - sqlalchemy.engine.Engine - INFO - [cached since 557.6s ago] {'analysis_id': 0, 'timestamp': '2025-08-03 19:32:58', 'type': 'openai', 'messages': '[\n    {\n        "role": "user",\n        "content": "\\nYou are an expert at extracting business information from policy documents and website cont ... (2726 characters truncated) ... \\"shipping_policy_details\\": \\"extracted shipping policy details or null\\"\\n}\\n\\nReturn only the JSON response, no additional text."\n    }\n]', 'response': '"{\\n    \\"shipping_countries\\": \\"null\\",\\n    \\"shipping_policy_details\\": \\"null\\"\\n}"', 'org_id': 'default'}
2025-08-03 19:33:00,915 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:33:00,915 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:33:01][api_response_logger][unknown] INFO: OpenAI API Call - Name: entity_extraction_shipping_info, Request ID: None
{
  "response_preview": "{\n    \"shipping_countries\": \"null\",\n    \"shipping_policy_details\": \"null\"\n}",
  "log_file": "api_logs/openai_20250803_193301_020.json",
  "clean_file": "api_logs/clean_openai_20250803_193301_020.json"
}
[API_LOGGER] OpenAI response logged to: api_logs/openai_20250803_193301_020.json
[API_LOGGER] Clean version at: api_logs/clean_openai_20250803_193301_020.json
[2025-08-03 19:33:01][api_response_logger][unknown] INFO: OpenAI API Call - Name: entity_extraction_shipping_info, Request ID: ************************************
{
  "response_preview": "{\n    \"shipping_countries\": \"null\",\n    \"shipping_policy_details\": \"null\"\n}",
  "log_file": "api_logs/openai_20250803_193301_021_************************************.json",
  "clean_file": "api_logs/clean_openai_20250803_193301_021_************************************.json"
}
[API_LOGGER] OpenAI response logged to: api_logs/openai_20250803_193301_021_************************************.json
[API_LOGGER] Clean version at: api_logs/clean_openai_20250803_193301_021_************************************.json
[2025-08-03 19:33:01][EntityExtractor][180][************************************] INFO: OpenAI shipping_info call extracted 0 fields: []
2025-08-03 19:33:01,065 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:33:01,065 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:33:01,065 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:33:01,065 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:33:01,065 INFO sqlalchemy.engine.Engine [cached since 557.8s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:33:01.021582', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "OpenAI shipping_info call extracted 0 fields: []", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:33:01.021577", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:33:01,065 - sqlalchemy.engine.Engine - INFO - [cached since 557.8s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:33:01.021582', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "OpenAI shipping_info call extracted 0 fields: []", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:33:01.021577", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:33:01,151 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:33:01,151 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:33:01][EntityExtractor][180][************************************] INFO: Making final comprehensive call for remaining fields: ['shipping_countries', 'shipping_policy_details']
2025-08-03 19:33:01,519 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:33:01,519 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:33:01,519 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:33:01,519 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:33:01,519 INFO sqlalchemy.engine.Engine [cached since 558.2s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:33:01.458503', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Making final comprehensive call for remaining fields: [\'shipping_countries\', \'shipping_policy_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:33:01.458495", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:33:01,519 - sqlalchemy.engine.Engine - INFO - [cached since 558.2s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:33:01.458503', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Making final comprehensive call for remaining fields: [\'shipping_countries\', \'shipping_policy_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:33:01.458495", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:33:01,580 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:33:01,580 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 19:33:02,827 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 19:33:02,895 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:33:02,895 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:33:02,896 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:33:02,896 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:33:02,896 INFO sqlalchemy.engine.Engine [cached since 559.6s ago] {'analysis_id': 0, 'timestamp': '2025-08-03 19:33:01', 'type': 'openai', 'messages': '[\n    {\n        "role": "user",\n        "content": "\\nYou are an expert at extracting business information from policy documents and website cont ... (14443 characters truncated) ... \\"shipping_policy_details\\": \\"extracted shipping policy details or null\\"\\n}\\n\\nReturn only the JSON response, no additional text."\n    }\n]', 'response': '"{\\n    \\"shipping_countries\\": \\"null\\",\\n    \\"shipping_policy_details\\": \\"null\\"\\n}"', 'org_id': 'default'}
2025-08-03 19:33:02,896 - sqlalchemy.engine.Engine - INFO - [cached since 559.6s ago] {'analysis_id': 0, 'timestamp': '2025-08-03 19:33:01', 'type': 'openai', 'messages': '[\n    {\n        "role": "user",\n        "content": "\\nYou are an expert at extracting business information from policy documents and website cont ... (14443 characters truncated) ... \\"shipping_policy_details\\": \\"extracted shipping policy details or null\\"\\n}\\n\\nReturn only the JSON response, no additional text."\n    }\n]', 'response': '"{\\n    \\"shipping_countries\\": \\"null\\",\\n    \\"shipping_policy_details\\": \\"null\\"\\n}"', 'org_id': 'default'}
2025-08-03 19:33:03,012 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:33:03,012 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:33:03][api_response_logger][unknown] INFO: OpenAI API Call - Name: entity_extraction_comprehensive, Request ID: None
{
  "response_preview": "{\n    \"shipping_countries\": \"null\",\n    \"shipping_policy_details\": \"null\"\n}",
  "log_file": "api_logs/openai_20250803_193303_112.json",
  "clean_file": "api_logs/clean_openai_20250803_193303_112.json"
}
[API_LOGGER] OpenAI response logged to: api_logs/openai_20250803_193303_112.json
[API_LOGGER] Clean version at: api_logs/clean_openai_20250803_193303_112.json
[2025-08-03 19:33:03][api_response_logger][unknown] INFO: OpenAI API Call - Name: entity_extraction_comprehensive, Request ID: ************************************
{
  "response_preview": "{\n    \"shipping_countries\": \"null\",\n    \"shipping_policy_details\": \"null\"\n}",
  "log_file": "api_logs/openai_20250803_193303_113_************************************.json",
  "clean_file": "api_logs/clean_openai_20250803_193303_113_************************************.json"
}
[API_LOGGER] OpenAI response logged to: api_logs/openai_20250803_193303_113_************************************.json
[API_LOGGER] Clean version at: api_logs/clean_openai_20250803_193303_113_************************************.json
[2025-08-03 19:33:03][EntityExtractor][180][************************************] INFO: OpenAI comprehensive call extracted 0 fields: []
2025-08-03 19:33:03,187 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:33:03,187 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:33:03,187 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:33:03,187 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:33:03,187 INFO sqlalchemy.engine.Engine [cached since 559.9s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:33:03.113808', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "OpenAI comprehensive call extracted 0 fields: []", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:33:03.113800", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:33:03,187 - sqlalchemy.engine.Engine - INFO - [cached since 559.9s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:33:03.113808', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "OpenAI comprehensive call extracted 0 fields: []", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:33:03.113800", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:33:03,270 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:33:03,270 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:33:03][EntityExtractor][180][************************************] INFO: Total OpenAI backup extracted 1 fields: ['support_email']
2025-08-03 19:33:03,630 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:33:03,630 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:33:03,630 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:33:03,630 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:33:03,630 INFO sqlalchemy.engine.Engine [cached since 560.4s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:33:03.576396', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Total OpenAI backup extracted 1 fields: [\'support_email\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:33:03.576388", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:33:03,630 - sqlalchemy.engine.Engine - INFO - [cached since 560.4s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:33:03.576396', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Total OpenAI backup extracted 1 fields: [\'support_email\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:33:03.576388", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:33:03,691 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:33:03,691 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:33:03][EntityExtractor][180][************************************] INFO: OpenAI backup extracted: ['support_email']
2025-08-03 19:33:03,839 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:33:03,839 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:33:03,840 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:33:03,840 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:33:03,840 INFO sqlalchemy.engine.Engine [cached since 560.6s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:33:03.791006', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "OpenAI backup extracted: [\'support_email\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:33:03.790998", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:33:03,840 - sqlalchemy.engine.Engine - INFO - [cached since 560.6s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:33:03.791006', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "OpenAI backup extracted: [\'support_email\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:33:03.790998", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:33:03,905 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:33:03,905 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:33:04][EntityExtractor][180][************************************] INFO: Merging 2 AI extraction results
2025-08-03 19:33:04,070 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:33:04,070 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:33:04,071 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:33:04,071 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:33:04,071 INFO sqlalchemy.engine.Engine [cached since 560.8s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:33:04.024158', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Merging 2 AI extraction results", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:33:04.024149", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:33:04,071 - sqlalchemy.engine.Engine - INFO - [cached since 560.8s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:33:04.024158', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Merging 2 AI extraction results", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:33:04.024149", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:33:04,147 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:33:04,147 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:33:04][EntityExtractor][180][************************************] INFO: Merged result contains 8 fields
2025-08-03 19:33:04,310 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:33:04,310 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:33:04,311 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:33:04,311 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:33:04,311 INFO sqlalchemy.engine.Engine [cached since 561s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:33:04.261111', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Merged result contains 8 fields", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:33:04.261104", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:33:04,311 - sqlalchemy.engine.Engine - INFO - [cached since 561s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:33:04.261111', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Merged result contains 8 fields", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:33:04.261104", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:33:04,371 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:33:04,371 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:33:04][EntityExtractor][180][************************************] INFO: Final merged extraction result keys: ['legal_name', 'business_email', 'business_contact_numbers', 'business_location', 'has_jurisdiction_law', 'jurisdiction_place', 'jurisdiction_details', 'support_email']
2025-08-03 19:33:04,533 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:33:04,533 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:33:04,534 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:33:04,534 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:33:04,534 INFO sqlalchemy.engine.Engine [cached since 561.3s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:33:04.470984', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Final merged extraction result keys: [\'legal_name\', \'business_email\', \'business_contact_numbers\', \'business_loca ... (75 characters truncated) ... tails\', \'support_email\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:33:04.470976", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:33:04,534 - sqlalchemy.engine.Engine - INFO - [cached since 561.3s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:33:04.470984', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Final merged extraction result keys: [\'legal_name\', \'business_email\', \'business_contact_numbers\', \'business_loca ... (75 characters truncated) ... tails\', \'support_email\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:33:04.470976", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:33:04,587 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:33:04,587 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:33:04][EntityExtractor][180][************************************] INFO: Fallback raw text for privacy_policy_text: present
2025-08-03 19:33:04,739 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:33:04,739 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:33:04,739 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:33:04,739 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:33:04,739 INFO sqlalchemy.engine.Engine [cached since 561.5s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:33:04.681191', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Fallback raw text for privacy_policy_text: present", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:33:04.681182", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:33:04,739 - sqlalchemy.engine.Engine - INFO - [cached since 561.5s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:33:04.681191', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Fallback raw text for privacy_policy_text: present", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:33:04.681182", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:33:04,809 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:33:04,809 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:33:04][EntityExtractor][180][************************************] INFO: After fallback, merged_result[privacy_policy_text] length: 2340
2025-08-03 19:33:04,962 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:33:04,962 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:33:04,962 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:33:04,962 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:33:04,962 INFO sqlalchemy.engine.Engine [cached since 561.7s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:33:04.911061', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "After fallback, merged_result[privacy_policy_text] length: 2340", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:33:04.911053", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:33:04,962 - sqlalchemy.engine.Engine - INFO - [cached since 561.7s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:33:04.911061', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "After fallback, merged_result[privacy_policy_text] length: 2340", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:33:04.911053", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:33:05,021 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:33:05,021 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:33:05][EntityExtractor][180][************************************] INFO: Fallback raw text for terms_conditions_text: present
2025-08-03 19:33:05,230 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:33:05,230 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:33:05,230 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:33:05,230 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:33:05,231 INFO sqlalchemy.engine.Engine [cached since 562s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:33:05.159920', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Fallback raw text for terms_conditions_text: present", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:33:05.159912", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:33:05,231 - sqlalchemy.engine.Engine - INFO - [cached since 562s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:33:05.159920', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Fallback raw text for terms_conditions_text: present", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:33:05.159912", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:33:05,358 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:33:05,358 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:33:05][EntityExtractor][180][************************************] INFO: After fallback, merged_result[terms_conditions_text] length: 1479
2025-08-03 19:33:05,632 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:33:05,632 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:33:05,632 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:33:05,632 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:33:05,632 INFO sqlalchemy.engine.Engine [cached since 562.4s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:33:05.564968', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "After fallback, merged_result[terms_conditions_text] length: 1479", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:33:05.564955", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:33:05,632 - sqlalchemy.engine.Engine - INFO - [cached since 562.4s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:33:05.564968', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "After fallback, merged_result[terms_conditions_text] length: 1479", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:33:05.564955", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:33:05,702 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:33:05,702 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 19:33:05,954 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:33:05,954 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:33:05,954 INFO sqlalchemy.engine.Engine SELECT entity_extraction_analysis.id AS entity_extraction_analysis_id, entity_extraction_analysis.scrape_request_ref_id AS entity_extraction_analysis_scrape_request_ref_id, entity_extraction_analysis.website_url AS entity_extraction_analysis_website_url, entity_extraction_analysis.processing_status AS entity_extraction_analysis_processing_status, entity_extraction_analysis.legal_name AS entity_extraction_analysis_legal_name, entity_extraction_analysis.business_email AS entity_extraction_analysis_business_email, entity_extraction_analysis.support_email AS entity_extraction_analysis_support_email, entity_extraction_analysis.business_contact_numbers AS entity_extraction_analysis_business_contact_numbers, entity_extraction_analysis.business_location AS entity_extraction_analysis_business_location, entity_extraction_analysis.has_jurisdiction_law AS entity_extraction_analysis_has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details AS entity_extraction_analysis_jurisdiction_details, entity_extraction_analysis.accepts_international_orders AS entity_extraction_analysis_accepts_international_orders, entity_extraction_analysis.shipping_policy_details AS entity_extraction_analysis_shipping_policy_details, entity_extraction_analysis.jurisdiction_place AS entity_extraction_analysis_jurisdiction_place, entity_extraction_analysis.shipping_countries AS entity_extraction_analysis_shipping_countries, entity_extraction_analysis.privacy_policy_text AS entity_extraction_analysis_privacy_policy_text, entity_extraction_analysis.terms_conditions_text AS entity_extraction_analysis_terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini AS entity_extraction_analysis_urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini AS entity_extraction_analysis_urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method AS entity_extraction_analysis_extraction_method, entity_extraction_analysis.total_urls_processed AS entity_extraction_analysis_total_urls_processed, entity_extraction_analysis.all_urls_found AS entity_extraction_analysis_all_urls_found, entity_extraction_analysis.reachable_urls AS entity_extraction_analysis_reachable_urls, entity_extraction_analysis.unreachable_urls AS entity_extraction_analysis_unreachable_urls, entity_extraction_analysis.policy_urls_matched AS entity_extraction_analysis_policy_urls_matched, entity_extraction_analysis.created_at AS entity_extraction_analysis_created_at, entity_extraction_analysis.started_at AS entity_extraction_analysis_started_at, entity_extraction_analysis.completed_at AS entity_extraction_analysis_completed_at, entity_extraction_analysis.error_message AS entity_extraction_analysis_error_message, entity_extraction_analysis.org_id AS entity_extraction_analysis_org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.id = %(pk_1)s
2025-08-03 19:33:05,954 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_analysis.id AS entity_extraction_analysis_id, entity_extraction_analysis.scrape_request_ref_id AS entity_extraction_analysis_scrape_request_ref_id, entity_extraction_analysis.website_url AS entity_extraction_analysis_website_url, entity_extraction_analysis.processing_status AS entity_extraction_analysis_processing_status, entity_extraction_analysis.legal_name AS entity_extraction_analysis_legal_name, entity_extraction_analysis.business_email AS entity_extraction_analysis_business_email, entity_extraction_analysis.support_email AS entity_extraction_analysis_support_email, entity_extraction_analysis.business_contact_numbers AS entity_extraction_analysis_business_contact_numbers, entity_extraction_analysis.business_location AS entity_extraction_analysis_business_location, entity_extraction_analysis.has_jurisdiction_law AS entity_extraction_analysis_has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details AS entity_extraction_analysis_jurisdiction_details, entity_extraction_analysis.accepts_international_orders AS entity_extraction_analysis_accepts_international_orders, entity_extraction_analysis.shipping_policy_details AS entity_extraction_analysis_shipping_policy_details, entity_extraction_analysis.jurisdiction_place AS entity_extraction_analysis_jurisdiction_place, entity_extraction_analysis.shipping_countries AS entity_extraction_analysis_shipping_countries, entity_extraction_analysis.privacy_policy_text AS entity_extraction_analysis_privacy_policy_text, entity_extraction_analysis.terms_conditions_text AS entity_extraction_analysis_terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini AS entity_extraction_analysis_urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini AS entity_extraction_analysis_urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method AS entity_extraction_analysis_extraction_method, entity_extraction_analysis.total_urls_processed AS entity_extraction_analysis_total_urls_processed, entity_extraction_analysis.all_urls_found AS entity_extraction_analysis_all_urls_found, entity_extraction_analysis.reachable_urls AS entity_extraction_analysis_reachable_urls, entity_extraction_analysis.unreachable_urls AS entity_extraction_analysis_unreachable_urls, entity_extraction_analysis.policy_urls_matched AS entity_extraction_analysis_policy_urls_matched, entity_extraction_analysis.created_at AS entity_extraction_analysis_created_at, entity_extraction_analysis.started_at AS entity_extraction_analysis_started_at, entity_extraction_analysis.completed_at AS entity_extraction_analysis_completed_at, entity_extraction_analysis.error_message AS entity_extraction_analysis_error_message, entity_extraction_analysis.org_id AS entity_extraction_analysis_org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.id = %(pk_1)s
2025-08-03 19:33:05,954 INFO sqlalchemy.engine.Engine [cached since 560.5s ago] {'pk_1': 180}
2025-08-03 19:33:05,954 - sqlalchemy.engine.Engine - INFO - [cached since 560.5s ago] {'pk_1': 180}
2025-08-03 19:33:06,032 INFO sqlalchemy.engine.Engine UPDATE entity_extraction_analysis SET processing_status=%(processing_status)s, business_location=%(business_location)s, jurisdiction_details=%(jurisdiction_details)s, accepts_international_orders=%(accepts_international_orders)s, completed_at=%(completed_at)s WHERE entity_extraction_analysis.id = %(entity_extraction_analysis_id)s
2025-08-03 19:33:06,032 - sqlalchemy.engine.Engine - INFO - UPDATE entity_extraction_analysis SET processing_status=%(processing_status)s, business_location=%(business_location)s, jurisdiction_details=%(jurisdiction_details)s, accepts_international_orders=%(accepts_international_orders)s, completed_at=%(completed_at)s WHERE entity_extraction_analysis.id = %(entity_extraction_analysis_id)s
2025-08-03 19:33:06,032 INFO sqlalchemy.engine.Engine [generated in 0.00017s] {'processing_status': 'COMPLETED', 'business_location': '2nd Floor, Campus 4A, RMZ Millenia Business Park, 143, Dr MGR Road, Perungudi, Chennai - 600 096, Commerzone, Block-2, No.2, 200 Feet Radial Road, Pallikaranai, Chennai – 600100 India', 'jurisdiction_details': 'Governing law details not explicitly provided in browsed content.', 'accepts_international_orders': None, 'completed_at': '2025-08-03T19:33:06.031271', 'entity_extraction_analysis_id': 180}
2025-08-03 19:33:06,032 - sqlalchemy.engine.Engine - INFO - [generated in 0.00017s] {'processing_status': 'COMPLETED', 'business_location': '2nd Floor, Campus 4A, RMZ Millenia Business Park, 143, Dr MGR Road, Perungudi, Chennai - 600 096, Commerzone, Block-2, No.2, 200 Feet Radial Road, Pallikaranai, Chennai – 600100 India', 'jurisdiction_details': 'Governing law details not explicitly provided in browsed content.', 'accepts_international_orders': None, 'completed_at': '2025-08-03T19:33:06.031271', 'entity_extraction_analysis_id': 180}
2025-08-03 19:33:06,102 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:33:06,102 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:33:06][EntityExtractor][180][************************************] INFO: Updated analysis 180 with merged results and completed_at timestamp
2025-08-03 19:33:06,272 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:33:06,272 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:33:06,272 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:33:06,272 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:33:06,272 INFO sqlalchemy.engine.Engine [cached since 563s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:33:06.220995', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Updated analysis 180 with merged results and completed_at timestamp", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:33:06.220983", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:33:06,272 - sqlalchemy.engine.Engine - INFO - [cached since 563s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:33:06.220995', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Updated analysis 180 with merged results and completed_at timestamp", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:33:06.220983", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:33:06,349 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:33:06,349 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:33:06][EntityExtractor][180][************************************] INFO: Creating response with merged_result keys: ['legal_name', 'business_email', 'business_contact_numbers', 'business_location', 'has_jurisdiction_law', 'jurisdiction_place', 'jurisdiction_details', 'support_email', 'privacy_policy_text', 'terms_conditions_text']
2025-08-03 19:33:06,615 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:33:06,615 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:33:06,615 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:33:06,615 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:33:06,615 INFO sqlalchemy.engine.Engine [cached since 563.3s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:33:06.488214', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Creating response with merged_result keys: [\'legal_name\', \'business_email\', \'business_contact_numbers\', \'busines ... (133 characters truncated) ...  \'terms_conditions_text\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:33:06.488207", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:33:06,615 - sqlalchemy.engine.Engine - INFO - [cached since 563.3s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:33:06.488214', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Creating response with merged_result keys: [\'legal_name\', \'business_email\', \'business_contact_numbers\', \'busines ... (133 characters truncated) ...  \'terms_conditions_text\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:33:06.488207", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:33:06,724 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:33:06,724 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:33:06][EntityExtractor][180][************************************] INFO: Response jurisdiction_details: Governing law details not explicitly provided in browsed content.
2025-08-03 19:33:06,881 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:33:06,881 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:33:06,881 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:33:06,881 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:33:06,881 INFO sqlalchemy.engine.Engine [cached since 563.6s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:33:06.831967', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Response jurisdiction_details: Governing law details not explicitly provided in browsed content.", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:33:06.831959", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:33:06,881 - sqlalchemy.engine.Engine - INFO - [cached since 563.6s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:33:06.831967', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Response jurisdiction_details: Governing law details not explicitly provided in browsed content.", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:33:06.831959", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:33:06,942 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:33:06,942 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:33:07][EntityExtractor][180][************************************] INFO: Response jurisdiction_place: ['India']
2025-08-03 19:33:07,126 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:33:07,126 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:33:07,126 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:33:07,126 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:33:07,126 INFO sqlalchemy.engine.Engine [cached since 563.9s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:33:07.069063', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Response jurisdiction_place: [\'India\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:33:07.069055", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:33:07,126 - sqlalchemy.engine.Engine - INFO - [cached since 563.9s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:33:07.069063', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Response jurisdiction_place: [\'India\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:33:07.069055", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:33:07,198 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:33:07,198 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:33:07][EntityExtractor][180][************************************] INFO: Response has_jurisdiction_law: yes
2025-08-03 19:33:07,382 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:33:07,382 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:33:07,382 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:33:07,382 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:33:07,382 INFO sqlalchemy.engine.Engine [cached since 564.1s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:33:07.331184', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Response has_jurisdiction_law: yes", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:33:07.331173", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:33:07,382 - sqlalchemy.engine.Engine - INFO - [cached since 564.1s ago] {'analysis_id': 180, 'timestamp': '2025-08-03T19:33:07.331184', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Response has_jurisdiction_law: yes", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T19:33:07.331173", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:33:07,439 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:33:07,439 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:33:07][EntityExtractor][background_processor][background] INFO: Entity extraction completed for https://www.shell.in
2025-08-03 19:33:07,600 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:33:07,600 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:33:07,600 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:33:07,600 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:33:07,600 INFO sqlalchemy.engine.Engine [cached since 564.3s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:33:07.551127', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Entity extraction completed for https://www.shell.in", "scrape_request_ref_id": "background", "timestamp": "2025-08-03T19:33:07.551122", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:33:07,600 - sqlalchemy.engine.Engine - INFO - [cached since 564.3s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:33:07.551127', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Entity extraction completed for https://www.shell.in", "scrape_request_ref_id": "background", "timestamp": "2025-08-03T19:33:07.551122", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:33:07,682 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:33:07,682 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:33:07][EntityExtractor][background_processor][background] INFO: Sending extraction results to Java server (sync) for https://www.shell.in
2025-08-03 19:33:07,842 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:33:07,842 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:33:07,842 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:33:07,842 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:33:07,842 INFO sqlalchemy.engine.Engine [cached since 564.6s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:33:07.788885', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Sending extraction results to Java server (sync) for https://www.shell.in", "scrape_request_ref_id": "background", "timestamp": "2025-08-03T19:33:07.788878", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:33:07,842 - sqlalchemy.engine.Engine - INFO - [cached since 564.6s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:33:07.788885', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Sending extraction results to Java server (sync) for https://www.shell.in", "scrape_request_ref_id": "background", "timestamp": "2025-08-03T19:33:07.788878", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:33:07,901 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:33:07,901 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:33:08][EntityExtractor][background_processor][background] INFO: Sending PATCH request to: http://localhost:8080/api/entity-extraction/results
2025-08-03 19:33:08,064 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:33:08,064 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:33:08,064 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:33:08,064 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:33:08,064 INFO sqlalchemy.engine.Engine [cached since 564.8s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:33:08.004656', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Sending PATCH request to: http://localhost:8080/api/entity-extraction/results", "scrape_request_ref_id": "background", "timestamp": "2025-08-03T19:33:08.004652", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:33:08,064 - sqlalchemy.engine.Engine - INFO - [cached since 564.8s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:33:08.004656', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Sending PATCH request to: http://localhost:8080/api/entity-extraction/results", "scrape_request_ref_id": "background", "timestamp": "2025-08-03T19:33:08.004652", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:33:08,112 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:33:08,112 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:33:08][EntityExtractor][background_processor][background] DEBUG: Payload org_id type: <class 'int'>, value: 2
2025-08-03 19:33:08,282 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:33:08,282 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:33:08,282 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:33:08,282 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:33:08,282 INFO sqlalchemy.engine.Engine [cached since 565s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:33:08.224365', 'type': 'entity_extractor', 'messages': '{"level": "DEBUG", "message": "Payload org_id type: <class \'int\'>, value: 2", "scrape_request_ref_id": "background", "timestamp": "2025-08-03T19:33:08.224357", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:33:08,282 - sqlalchemy.engine.Engine - INFO - [cached since 565s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:33:08.224365', 'type': 'entity_extractor', 'messages': '{"level": "DEBUG", "message": "Payload org_id type: <class \'int\'>, value: 2", "scrape_request_ref_id": "background", "timestamp": "2025-08-03T19:33:08.224357", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:33:08,341 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:33:08,341 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:33:08][EntityExtractor][background_processor][background] ERROR: Request error sending results to Java server: [Errno 111] Connection refused
2025-08-03 19:33:08,501 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:33:08,501 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:33:08,502 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:33:08,502 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:33:08,502 INFO sqlalchemy.engine.Engine [cached since 565.2s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:33:08.452686', 'type': 'entity_extractor', 'messages': '{"level": "ERROR", "message": "Request error sending results to Java server: [Errno 111] Connection refused", "scrape_request_ref_id": "background", "timestamp": "2025-08-03T19:33:08.452679", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:33:08,502 - sqlalchemy.engine.Engine - INFO - [cached since 565.2s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:33:08.452686', 'type': 'entity_extractor', 'messages': '{"level": "ERROR", "message": "Request error sending results to Java server: [Errno 111] Connection refused", "scrape_request_ref_id": "background", "timestamp": "2025-08-03T19:33:08.452679", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:33:08,564 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:33:08,564 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 19:33:08][EntityExtractor][background_processor][background] ERROR: Failed to send results to Java server: Request error sending results to Java server: [Errno 111] Connection refused
2025-08-03 19:33:08,721 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 19:33:08,721 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 19:33:08,721 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:33:08,721 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 19:33:08,721 INFO sqlalchemy.engine.Engine [cached since 565.4s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:33:08.671091', 'type': 'entity_extractor', 'messages': '{"level": "ERROR", "message": "Failed to send results to Java server: Request error sending results to Java server: [Errno 111] Connection refused", "scrape_request_ref_id": "background", "timestamp": "2025-08-03T19:33:08.671084", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:33:08,721 - sqlalchemy.engine.Engine - INFO - [cached since 565.4s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T19:33:08.671091', 'type': 'entity_extractor', 'messages': '{"level": "ERROR", "message": "Failed to send results to Java server: Request error sending results to Java server: [Errno 111] Connection refused", "scrape_request_ref_id": "background", "timestamp": "2025-08-03T19:33:08.671084", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 19:33:08,782 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 19:33:08,782 - sqlalchemy.engine.Engine - INFO - COMMIT
