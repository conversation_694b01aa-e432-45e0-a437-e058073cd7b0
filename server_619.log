nohup: ignoring input
INFO:     Started server process [85165]
INFO:     Waiting for application startup.
2025-08-03 18:58:05,061 - app.main - INFO - Initializing Entity Extraction API
2025-08-03 18:58:05,435 INFO sqlalchemy.engine.Engine SELECT DATABASE()
2025-08-03 18:58:05,435 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-03 18:58:05,435 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 18:58:05,435 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 18:58:05,555 INFO sqlalchemy.engine.Engine SELECT @@sql_mode
2025-08-03 18:58:05,555 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-03 18:58:05,555 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 18:58:05,555 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 18:58:05,605 INFO sqlalchemy.engine.Engine SELECT @@lower_case_table_names
2025-08-03 18:58:05,605 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-03 18:58:05,605 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 18:58:05,605 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 18:58:05,705 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:58:05,705 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:58:05,705 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`website_urls_gemini`
2025-08-03 18:58:05,705 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`website_urls_gemini`
2025-08-03 18:58:05,705 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 18:58:05,705 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 18:58:05,765 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`general_logs_gemini`
2025-08-03 18:58:05,765 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`general_logs_gemini`
2025-08-03 18:58:05,765 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 18:58:05,765 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 18:58:05,826 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`mcc_url_classification_gemini`
2025-08-03 18:58:05,826 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`mcc_url_classification_gemini`
2025-08-03 18:58:05,826 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 18:58:05,826 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 18:58:05,886 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`policy_url_classification_gemini`
2025-08-03 18:58:05,886 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`policy_url_classification_gemini`
2025-08-03 18:58:05,886 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 18:58:05,886 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 18:58:05,947 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`policy_analysis_new_gemini`
2025-08-03 18:58:05,947 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`policy_analysis_new_gemini`
2025-08-03 18:58:05,947 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 18:58:05,947 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 18:58:06,009 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`entity_extraction_analysis`
2025-08-03 18:58:06,009 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`entity_extraction_analysis`
2025-08-03 18:58:06,009 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 18:58:06,009 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 18:58:06,078 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`entity_extraction_url_analysis`
2025-08-03 18:58:06,078 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`entity_extraction_url_analysis`
2025-08-03 18:58:06,078 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 18:58:06,078 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 18:58:06,154 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:58:06,154 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 18:58:06,255 - app.main - INFO - Database initialized successfully
Creating Entity Extractor database tables...
2025-08-03 18:58:06,315 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:58:06,315 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:58:06,315 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`website_urls_gemini`
2025-08-03 18:58:06,315 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`website_urls_gemini`
2025-08-03 18:58:06,315 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 18:58:06,315 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 18:58:06,378 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`general_logs_gemini`
2025-08-03 18:58:06,378 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`general_logs_gemini`
2025-08-03 18:58:06,378 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 18:58:06,378 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 18:58:06,444 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`mcc_url_classification_gemini`
2025-08-03 18:58:06,444 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`mcc_url_classification_gemini`
2025-08-03 18:58:06,444 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 18:58:06,444 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 18:58:06,504 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`policy_url_classification_gemini`
2025-08-03 18:58:06,504 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`policy_url_classification_gemini`
2025-08-03 18:58:06,504 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 18:58:06,504 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 18:58:06,556 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`policy_analysis_new_gemini`
2025-08-03 18:58:06,556 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`policy_analysis_new_gemini`
2025-08-03 18:58:06,556 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 18:58:06,556 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 18:58:06,618 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`entity_extraction_analysis`
2025-08-03 18:58:06,618 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`entity_extraction_analysis`
2025-08-03 18:58:06,618 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 18:58:06,618 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
