#!/usr/bin/env python3
"""
Dummy Java Server for Testing PATCH Calls

This script simulates a Java server that receives PATCH requests from the entity extraction API.
It runs on port 8082 and logs all incoming requests for testing purposes.
"""

import json
import logging
from datetime import datetime
from typing import Dict, Any

from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException
from fastapi.responses import JSONResponse
import uvicorn

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('dummy_java_server.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("dummy_java_server")

# Create FastAPI app
app = FastAPI(
    title="Dummy Java Server",
    description="Mock Java server for testing PATCH calls from entity extraction API",
    version="1.0.0"
)

# Store received requests for inspection
received_requests = []


@app.patch("/api/entity-extraction/results")
async def receive_extraction_results(request: Request):
    """
    Endpoint to receive entity extraction results via PATCH
    """
    try:
        # Get request body
        body = await request.body()
        
        # Parse JSON
        try:
            data = json.loads(body.decode('utf-8'))
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in request: {e}")
            raise HTTPException(status_code=400, detail="Invalid JSON")
        
        # Log the received request
        timestamp = datetime.now().isoformat()
        request_info = {
            "timestamp": timestamp,
            "method": request.method,
            "url": str(request.url),
            "headers": dict(request.headers),
            "body": data
        }
        
        # Store for inspection
        received_requests.append(request_info)
        
        # Log to console and file
        logger.info("=" * 80)
        logger.info("RECEIVED PATCH REQUEST")
        logger.info("=" * 80)
        logger.info(f"Timestamp: {timestamp}")
        logger.info(f"URL: {request.url}")
        logger.info(f"Headers: {dict(request.headers)}")
        logger.info("Body:")
        logger.info(json.dumps(data, indent=2))
        logger.info("=" * 80)
        
        # Validate required fields
        required_fields = ["scrape_request_ref_id", "website_url", "org_id", "extraction_results"]
        missing_fields = [field for field in required_fields if field not in data]
        
        if missing_fields:
            logger.warning(f"Missing required fields: {missing_fields}")
            return JSONResponse(
                status_code=400,
                content={
                    "success": False,
                    "error": f"Missing required fields: {missing_fields}",
                    "timestamp": timestamp
                }
            )
        
        # Check org_id type preservation
        org_id = data.get("org_id")
        org_id_type = type(org_id).__name__
        logger.info(f"Org ID: {org_id} (type: {org_id_type})")
        
        # Simulate successful processing
        response_data = {
            "success": True,
            "message": "Entity extraction results received successfully",
            "timestamp": timestamp,
            "received_data": {
                "scrape_request_ref_id": data.get("scrape_request_ref_id"),
                "website_url": data.get("website_url"),
                "org_id": org_id,
                "org_id_type": org_id_type,
                "extraction_results_keys": list(data.get("extraction_results", {}).keys()) if isinstance(data.get("extraction_results"), dict) else "non-dict",
                "processing_status": data.get("processing_status")
            }
        }
        
        logger.info(f"Sending response: {json.dumps(response_data, indent=2)}")
        
        return JSONResponse(
            status_code=200,
            content=response_data
        )
        
    except Exception as e:
        logger.error(f"Error processing PATCH request: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "error": f"Internal server error: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }
        )


@app.get("/api/requests")
async def get_received_requests():
    """
    Get all received requests for inspection
    """
    return {
        "total_requests": len(received_requests),
        "requests": received_requests
    }


@app.get("/api/requests/latest")
async def get_latest_request():
    """
    Get the latest received request
    """
    if not received_requests:
        return {"message": "No requests received yet"}
    
    return {
        "latest_request": received_requests[-1],
        "total_requests": len(received_requests)
    }


@app.delete("/api/requests")
async def clear_requests():
    """
    Clear all stored requests
    """
    global received_requests
    count = len(received_requests)
    received_requests.clear()
    logger.info(f"Cleared {count} stored requests")
    return {"message": f"Cleared {count} requests"}


@app.get("/health")
async def health_check():
    """
    Health check endpoint
    """
    return {
        "status": "ok",
        "message": "Dummy Java server is running",
        "timestamp": datetime.now().isoformat(),
        "requests_received": len(received_requests)
    }


@app.get("/")
async def root():
    """
    Root endpoint with server info
    """
    return {
        "message": "Dummy Java Server for Entity Extraction Testing",
        "version": "1.0.0",
        "endpoints": {
            "PATCH /api/entity-extraction/results": "Receive entity extraction results",
            "GET /api/requests": "View all received requests",
            "GET /api/requests/latest": "View latest request",
            "DELETE /api/requests": "Clear stored requests",
            "GET /health": "Health check"
        },
        "requests_received": len(received_requests),
        "timestamp": datetime.now().isoformat()
    }


if __name__ == "__main__":
    print("=" * 60)
    print("DUMMY JAVA SERVER")
    print("=" * 60)
    print("This server simulates a Java server that receives PATCH requests")
    print("from the entity extraction API for testing purposes.")
    print("")
    print("Server will run on: http://localhost:8082")
    print("PATCH endpoint: http://localhost:8082/api/entity-extraction/results")
    print("View requests: http://localhost:8082/api/requests")
    print("Health check: http://localhost:8082/health")
    print("")
    print("Logs will be written to: dummy_java_server.log")
    print("=" * 60)
    
    # Start the server
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8082,
        log_level="info"
    )
