# Entity Extraction API Testing Setup

This document explains how to test the entity extraction API with the new logging improvements and PATCH call functionality.

## Overview

The testing setup includes:
1. **Enhanced API Response Logging** - Clean, readable Gemini and OpenAI responses in JSON files
2. **Main Server** - Entity extraction API running on port 8081
3. **Dummy Java Server** - Mock Java server on port 8082 to receive PATCH calls
4. **Test Scripts** - Automated testing of the complete workflow

## Files Created

### 1. Enhanced Logging
- **`app/Extractor/utils/api_response_logger.py`** - Enhanced to create detailed JSON log files

### 2. Server Scripts
- **`dummy_java_server.py`** - Mock Java server that receives PATCH calls
- **`start_main_server.py`** - Starts main API server on port 8081
- **`test_patch_calls.py`** - Tests the complete PATCH call workflow

## Quick Start

### Step 1: Start the Dummy Java Server
```bash
# Terminal 1
python dummy_java_server.py
```
This starts the mock Java server on port 8082 that will receive PATCH calls.

### Step 2: Start the Main API Server
```bash
# Terminal 2
python start_main_server.py
```
This starts the main entity extraction API on port 8081.

### Step 3: Run Tests
```bash
# Terminal 3
python test_patch_calls.py
```
This tests the complete workflow including org_id type preservation.

## What's Been Fixed

### 1. API Response Logging
The enhanced `APIResponseLogger` now creates:

- **Detailed JSON logs** in `api_logs/` directory
- **Clean readable versions** with `clean_` prefix
- **Console output** showing log file locations
- **Full response text** for analysis

Example log files:
```
api_logs/
├── gemini_20241203_143022_123_test_request.json
├── clean_gemini_20241203_143022_123_test_request.json
├── openai_20241203_143025_456_test_request.json
└── clean_openai_20241203_143025_456_test_request.json
```

### 2. PATCH Call Testing
The dummy Java server:

- **Receives PATCH requests** from the main API
- **Logs all requests** to console and file
- **Preserves org_id types** (int/string)
- **Provides inspection endpoints** to view received data

## Testing Workflow

### 1. Basic Entity Extraction Test
```bash
curl -X POST "http://localhost:8081/api/extract-entities" \
  -H "Content-Type: application/json" \
  -d '{
    "website_url": "https://example.com",
    "org_id": 12345,
    "scrape_request_ref_id": "test_123",
    "use_merged_process": true,
    "use_openai_fallback": true,
    "force_reprocess": true
  }'
```

### 2. Check API Response Logs
```bash
# View log directory
ls -la api_logs/

# View clean Gemini response
cat api_logs/clean_gemini_*.json

# View clean OpenAI response  
cat api_logs/clean_openai_*.json
```

### 3. Check PATCH Calls Received
```bash
# View all received requests
curl http://localhost:8082/api/requests

# View latest request only
curl http://localhost:8082/api/requests/latest

# Clear stored requests
curl -X DELETE http://localhost:8082/api/requests
```

## Server Endpoints

### Main Server (Port 8081)
- `POST /api/extract-entities` - Entity extraction endpoint
- `GET /health` - Health check
- `GET /docs` - API documentation

### Dummy Java Server (Port 8082)
- `PATCH /api/entity-extraction/results` - Receives extraction results
- `GET /api/requests` - View all received requests
- `GET /api/requests/latest` - View latest request
- `DELETE /api/requests` - Clear stored requests
- `GET /health` - Health check

## Log Files

### API Response Logs
- **Location**: `api_logs/` directory
- **Format**: JSON files with timestamps
- **Types**: 
  - Detailed logs: Full API response data
  - Clean logs: Readable prompt/response pairs

### Server Logs
- **Main server**: Standard FastAPI logs
- **Dummy Java server**: `dummy_java_server.log`

## Verification Checklist

✅ **API Response Logging**
- [ ] `api_logs/` directory is created
- [ ] Gemini responses are logged to JSON files
- [ ] OpenAI responses are logged to JSON files
- [ ] Clean versions are readable and contain full response text
- [ ] Console shows log file paths

✅ **PATCH Call Functionality**
- [ ] Main server returns 200 OK immediately
- [ ] Background processing sends PATCH to dummy Java server
- [ ] Dummy Java server receives and logs PATCH requests
- [ ] org_id type is preserved (int stays int, string stays string)
- [ ] All required fields are included in PATCH body

✅ **Server Communication**
- [ ] Main server (8081) is accessible
- [ ] Dummy Java server (8082) is accessible
- [ ] PATCH calls are successfully sent between servers
- [ ] Error handling works correctly

## Troubleshooting

### No Log Files Created
- Check if `api_logs/` directory exists
- Verify API calls are actually being made
- Check console output for error messages

### PATCH Calls Not Received
- Ensure dummy Java server is running on port 8082
- Check `JAVA_SERVER_URL` environment variable
- Verify background tasks are executing

### Type Preservation Issues
- Check the received request body in dummy Java server logs
- Compare sent vs received org_id types
- Verify JSON serialization is not converting types

## Environment Variables

```bash
# Required for PATCH calls
export JAVA_SERVER_URL="http://localhost:8082"

# API keys (if testing with real APIs)
export GEMINI="your_gemini_api_key"
export OPENAI_API_KEY="your_openai_api_key"
```

## Next Steps

After successful testing:
1. Update production `JAVA_SERVER_URL` to point to real Java server
2. Deploy enhanced logging to production
3. Monitor log files for API response analysis
4. Use clean log files for debugging and optimization
